{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\components\\\\GameLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100vh;\n  position: relative;\n  z-index: 10;\n`;\n_c = LoginContainer;\nconst LoginBox = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);\n  border: 2px solid #ffd700;\n  min-width: 350px;\n`;\n_c2 = LoginBox;\nconst Title = styled.h1`\n  color: #ffd700;\n  text-align: center;\n  font-size: 2.5rem;\n  margin-bottom: 30px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);\n  font-family: 'Microsoft YaHei', sans-serif;\n`;\n_c3 = Title;\nconst Subtitle = styled.p`\n  color: #fff;\n  text-align: center;\n  margin-bottom: 30px;\n  font-size: 1.1rem;\n`;\n_c4 = Subtitle;\nconst InputGroup = styled.div`\n  margin-bottom: 20px;\n`;\n_c5 = InputGroup;\nconst Label = styled.label`\n  display: block;\n  color: #ffd700;\n  margin-bottom: 8px;\n  font-weight: bold;\n`;\n_c6 = Label;\nconst Input = styled.input`\n  width: 100%;\n  padding: 12px;\n  border: 2px solid #ffd700;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: #fff;\n  font-size: 16px;\n  box-sizing: border-box;\n  \n  &:focus {\n    outline: none;\n    border-color: #ffed4e;\n    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\n  }\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.6);\n  }\n`;\n_c7 = Input;\nconst LoginButton = styled.button`\n  width: 100%;\n  padding: 15px;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  border: none;\n  border-radius: 8px;\n  color: #000;\n  font-size: 18px;\n  font-weight: bold;\n  cursor: pointer;\n  margin-top: 20px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n`;\n_c8 = LoginButton;\nconst QuickLoginButton = styled.button`\n  width: 100%;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #fff;\n  border-radius: 8px;\n  color: #fff;\n  font-size: 16px;\n  cursor: pointer;\n  margin-top: 10px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    border-color: #ffd700;\n    color: #ffd700;\n  }\n`;\n_c9 = QuickLoginButton;\nconst GameLogin = ({\n  onLogin\n}) => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const handleLogin = async () => {\n    if (!username.trim()) {\n      alert('请输入用户名');\n      return;\n    }\n    setIsLoading(true);\n\n    // 模拟登录请求\n    setTimeout(() => {\n      const userData = {\n        id: Date.now().toString(),\n        username: username.trim(),\n        vipLevel: 1,\n        diamonds: 100,\n        coins: 1000,\n        energy: 100,\n        maxEnergy: 100,\n        loginTime: new Date().toISOString()\n      };\n      onLogin(userData);\n      setIsLoading(false);\n    }, 1000);\n  };\n  const handleQuickLogin = () => {\n    const guestName = `游客${Math.floor(Math.random() * 10000)}`;\n    setUsername(guestName);\n    setTimeout(() => {\n      const userData = {\n        id: Date.now().toString(),\n        username: guestName,\n        vipLevel: 0,\n        diamonds: 50,\n        coins: 500,\n        energy: 100,\n        maxEnergy: 100,\n        loginTime: new Date().toISOString()\n      };\n      onLogin(userData);\n    }, 500);\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleLogin();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(LoginContainer, {\n    children: /*#__PURE__*/_jsxDEV(LoginBox, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\u6597\\u7F57\\u5927\\u9646\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n        children: \"\\u9B42\\u5E08\\u7684\\u4E16\\u754C\\u7B49\\u4F60\\u6765\\u5F81\\u670D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          children: \"\\u7528\\u6237\\u540D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          type: \"text\",\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n          value: username,\n          onChange: e => setUsername(e.target.value),\n          onKeyPress: handleKeyPress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Label, {\n          children: \"\\u5BC6\\u7801\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          type: \"password\",\n          placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\",\n          value: password,\n          onChange: e => setPassword(e.target.value),\n          onKeyPress: handleKeyPress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LoginButton, {\n        onClick: handleLogin,\n        disabled: isLoading,\n        children: isLoading ? '登录中...' : '进入游戏'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(QuickLoginButton, {\n        onClick: handleQuickLogin,\n        children: \"\\u6E38\\u5BA2\\u767B\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n};\n_s(GameLogin, \"IQe36BkbposlWQz1cSIF+3ZFDu4=\");\n_c0 = GameLogin;\nexport default GameLogin;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"LoginContainer\");\n$RefreshReg$(_c2, \"LoginBox\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"Subtitle\");\n$RefreshReg$(_c5, \"InputGroup\");\n$RefreshReg$(_c6, \"Label\");\n$RefreshReg$(_c7, \"Input\");\n$RefreshReg$(_c8, \"LoginButton\");\n$RefreshReg$(_c9, \"QuickLoginButton\");\n$RefreshReg$(_c0, \"GameLogin\");", "map": {"version": 3, "names": ["React", "useState", "styled", "jsxDEV", "_jsxDEV", "LoginContainer", "div", "_c", "LoginBox", "_c2", "Title", "h1", "_c3", "Subtitle", "p", "_c4", "InputGroup", "_c5", "Label", "label", "_c6", "Input", "input", "_c7", "LoginButton", "button", "_c8", "QuickLoginButton", "_c9", "GameLogin", "onLogin", "_s", "username", "setUsername", "password", "setPassword", "isLoading", "setIsLoading", "handleLogin", "trim", "alert", "setTimeout", "userData", "id", "Date", "now", "toString", "vipLevel", "diamonds", "coins", "energy", "maxEnergy", "loginTime", "toISOString", "handleQuickLogin", "<PERSON><PERSON><PERSON>", "Math", "floor", "random", "handleKeyPress", "e", "key", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "onKeyPress", "onClick", "disabled", "_c0", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/components/GameLogin.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\n\nconst LoginContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100vh;\n  position: relative;\n  z-index: 10;\n`;\n\nconst LoginBox = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);\n  border: 2px solid #ffd700;\n  min-width: 350px;\n`;\n\nconst Title = styled.h1`\n  color: #ffd700;\n  text-align: center;\n  font-size: 2.5rem;\n  margin-bottom: 30px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);\n  font-family: 'Microsoft YaHei', sans-serif;\n`;\n\nconst Subtitle = styled.p`\n  color: #fff;\n  text-align: center;\n  margin-bottom: 30px;\n  font-size: 1.1rem;\n`;\n\nconst InputGroup = styled.div`\n  margin-bottom: 20px;\n`;\n\nconst Label = styled.label`\n  display: block;\n  color: #ffd700;\n  margin-bottom: 8px;\n  font-weight: bold;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 12px;\n  border: 2px solid #ffd700;\n  border-radius: 8px;\n  background: rgba(255, 255, 255, 0.1);\n  color: #fff;\n  font-size: 16px;\n  box-sizing: border-box;\n  \n  &:focus {\n    outline: none;\n    border-color: #ffed4e;\n    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\n  }\n  \n  &::placeholder {\n    color: rgba(255, 255, 255, 0.6);\n  }\n`;\n\nconst LoginButton = styled.button`\n  width: 100%;\n  padding: 15px;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  border: none;\n  border-radius: 8px;\n  color: #000;\n  font-size: 18px;\n  font-weight: bold;\n  cursor: pointer;\n  margin-top: 20px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);\n  }\n  \n  &:active {\n    transform: translateY(0);\n  }\n`;\n\nconst QuickLoginButton = styled.button`\n  width: 100%;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #fff;\n  border-radius: 8px;\n  color: #fff;\n  font-size: 16px;\n  cursor: pointer;\n  margin-top: 10px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    border-color: #ffd700;\n    color: #ffd700;\n  }\n`;\n\ninterface GameLoginProps {\n  onLogin: (userData: any) => void;\n}\n\nconst GameLogin: React.FC<GameLoginProps> = ({ onLogin }) => {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleLogin = async () => {\n    if (!username.trim()) {\n      alert('请输入用户名');\n      return;\n    }\n    \n    setIsLoading(true);\n    \n    // 模拟登录请求\n    setTimeout(() => {\n      const userData = {\n        id: Date.now().toString(),\n        username: username.trim(),\n        vipLevel: 1,\n        diamonds: 100,\n        coins: 1000,\n        energy: 100,\n        maxEnergy: 100,\n        loginTime: new Date().toISOString(),\n      };\n      \n      onLogin(userData);\n      setIsLoading(false);\n    }, 1000);\n  };\n\n  const handleQuickLogin = () => {\n    const guestName = `游客${Math.floor(Math.random() * 10000)}`;\n    setUsername(guestName);\n    setTimeout(() => {\n      const userData = {\n        id: Date.now().toString(),\n        username: guestName,\n        vipLevel: 0,\n        diamonds: 50,\n        coins: 500,\n        energy: 100,\n        maxEnergy: 100,\n        loginTime: new Date().toISOString(),\n      };\n      \n      onLogin(userData);\n    }, 500);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      handleLogin();\n    }\n  };\n\n  return (\n    <LoginContainer>\n      <LoginBox>\n        <Title>斗罗大陆</Title>\n        <Subtitle>魂师的世界等你来征服</Subtitle>\n        \n        <InputGroup>\n          <Label>用户名</Label>\n          <Input\n            type=\"text\"\n            placeholder=\"请输入用户名\"\n            value={username}\n            onChange={(e) => setUsername(e.target.value)}\n            onKeyPress={handleKeyPress}\n          />\n        </InputGroup>\n        \n        <InputGroup>\n          <Label>密码</Label>\n          <Input\n            type=\"password\"\n            placeholder=\"请输入密码\"\n            value={password}\n            onChange={(e) => setPassword(e.target.value)}\n            onKeyPress={handleKeyPress}\n          />\n        </InputGroup>\n        \n        <LoginButton onClick={handleLogin} disabled={isLoading}>\n          {isLoading ? '登录中...' : '进入游戏'}\n        </LoginButton>\n        \n        <QuickLoginButton onClick={handleQuickLogin}>\n          游客登录\n        </QuickLoginButton>\n      </LoginBox>\n    </LoginContainer>\n  );\n};\n\nexport default GameLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,cAAc;AAUpB,MAAMG,QAAQ,GAAGN,MAAM,CAACI,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,QAAQ;AASd,MAAME,KAAK,GAAGR,MAAM,CAACS,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,KAAK;AASX,MAAMG,QAAQ,GAAGX,MAAM,CAACY,CAAC;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,QAAQ;AAOd,MAAMG,UAAU,GAAGd,MAAM,CAACI,GAAG;AAC7B;AACA,CAAC;AAACW,GAAA,GAFID,UAAU;AAIhB,MAAME,KAAK,GAAGhB,MAAM,CAACiB,KAAK;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,KAAK,GAAGnB,MAAM,CAACoB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,KAAK;AAqBX,MAAMG,WAAW,GAAGtB,MAAM,CAACuB,MAAM;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GArBIF,WAAW;AAuBjB,MAAMG,gBAAgB,GAAGzB,MAAM,CAACuB,MAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAjBID,gBAAgB;AAuBtB,MAAME,SAAmC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMqC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACN,QAAQ,CAACO,IAAI,CAAC,CAAC,EAAE;MACpBC,KAAK,CAAC,QAAQ,CAAC;MACf;IACF;IAEAH,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAI,UAAU,CAAC,MAAM;MACf,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBd,QAAQ,EAAEA,QAAQ,CAACO,IAAI,CAAC,CAAC;QACzBQ,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,GAAG;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,GAAG;QACXC,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACpC,CAAC;MAEDvB,OAAO,CAACY,QAAQ,CAAC;MACjBL,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAG,KAAKC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE;IAC1DzB,WAAW,CAACsB,SAAS,CAAC;IACtBd,UAAU,CAAC,MAAM;MACf,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBd,QAAQ,EAAEuB,SAAS;QACnBR,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,SAAS,EAAE,GAAG;QACdC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACpC,CAAC;MAEDvB,OAAO,CAACY,QAAQ,CAAC;IACnB,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMiB,cAAc,GAAIC,CAAsB,IAAK;IACjD,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBvB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,oBACElC,OAAA,CAACC,cAAc;IAAAyD,QAAA,eACb1D,OAAA,CAACI,QAAQ;MAAAsD,QAAA,gBACP1D,OAAA,CAACM,KAAK;QAAAoD,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnB9D,OAAA,CAACS,QAAQ;QAAAiD,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eAE/B9D,OAAA,CAACY,UAAU;QAAA8C,QAAA,gBACT1D,OAAA,CAACc,KAAK;UAAA4C,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClB9D,OAAA,CAACiB,KAAK;UACJ8C,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,sCAAQ;UACpBC,KAAK,EAAErC,QAAS;UAChBsC,QAAQ,EAAGV,CAAC,IAAK3B,WAAW,CAAC2B,CAAC,CAACW,MAAM,CAACF,KAAK,CAAE;UAC7CG,UAAU,EAAEb;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAEb9D,OAAA,CAACY,UAAU;QAAA8C,QAAA,gBACT1D,OAAA,CAACc,KAAK;UAAA4C,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjB9D,OAAA,CAACiB,KAAK;UACJ8C,IAAI,EAAC,UAAU;UACfC,WAAW,EAAC,gCAAO;UACnBC,KAAK,EAAEnC,QAAS;UAChBoC,QAAQ,EAAGV,CAAC,IAAKzB,WAAW,CAACyB,CAAC,CAACW,MAAM,CAACF,KAAK,CAAE;UAC7CG,UAAU,EAAEb;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAEb9D,OAAA,CAACoB,WAAW;QAACiD,OAAO,EAAEnC,WAAY;QAACoC,QAAQ,EAAEtC,SAAU;QAAA0B,QAAA,EACpD1B,SAAS,GAAG,QAAQ,GAAG;MAAM;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAEd9D,OAAA,CAACuB,gBAAgB;QAAC8C,OAAO,EAAEnB,gBAAiB;QAAAQ,QAAA,EAAC;MAE7C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAkB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAErB,CAAC;AAACnC,EAAA,CA9FIF,SAAmC;AAAA8C,GAAA,GAAnC9C,SAAmC;AAgGzC,eAAeA,SAAS;AAAC,IAAAtB,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA+C,GAAA;AAAAC,YAAA,CAAArE,EAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}