{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\components\\\\CharacterPanel.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PanelContainer = styled.div`\n  flex: 1;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n`;\n_c = PanelContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n`;\n_c2 = Header;\nconst Title = styled.h2`\n  color: #ffd700;\n  margin: 0;\n  font-size: 2rem;\n`;\n_c3 = Title;\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #ffd700;\n  color: #ffd700;\n  padding: 10px 20px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n    transform: translateY(-2px);\n  }\n`;\n_c4 = BackButton;\nconst CharacterDisplay = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 30px;\n  flex: 1;\n`;\n_c5 = CharacterDisplay;\nconst CharacterInfo = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 15px;\n  padding: 30px;\n  border: 2px solid #ffd700;\n`;\n_c6 = CharacterInfo;\nconst CharacterAvatar = styled.div`\n  width: 150px;\n  height: 150px;\n  border-radius: 50%;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 80px;\n  border: 5px solid #fff;\n  margin: 0 auto 20px;\n`;\n_c7 = CharacterAvatar;\nconst CharacterName = styled.h3`\n  color: #ffd700;\n  text-align: center;\n  font-size: 1.8rem;\n  margin-bottom: 20px;\n`;\n_c8 = CharacterName;\nconst StatsList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n`;\n_c9 = StatsList;\nconst StatItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  border: 1px solid #ffd700;\n`;\n_c0 = StatItem;\nconst StatLabel = styled.span`\n  color: #fff;\n  font-weight: bold;\n`;\n_c1 = StatLabel;\nconst StatValue = styled.span`\n  color: #ffd700;\n  font-weight: bold;\n  font-size: 1.1rem;\n`;\n_c10 = StatValue;\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 4px;\n  overflow: hidden;\n  margin-top: 5px;\n`;\n_c11 = ProgressBar;\nconst ProgressFill = styled.div`\n  width: ${props => props.percentage}%;\n  height: 100%;\n  background: ${props => props.color || 'linear-gradient(90deg, #4ecdc4, #44a08d)'};\n  transition: width 0.3s ease;\n`;\n_c12 = ProgressFill;\nconst EquipmentSection = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 15px;\n  padding: 30px;\n  border: 2px solid #ffd700;\n`;\n_c13 = EquipmentSection;\nconst SectionTitle = styled.h3`\n  color: #ffd700;\n  margin-bottom: 20px;\n  font-size: 1.5rem;\n`;\n_c14 = SectionTitle;\nconst EquipmentGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n_c15 = EquipmentGrid;\nconst EquipmentSlot = styled.div`\n  width: 80px;\n  height: 80px;\n  border: 2px solid ${props => props.hasItem ? '#ffd700' : '#666'};\n  border-radius: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ${props => props.hasItem ? 'rgba(255, 215, 0, 0.2)' : 'rgba(255, 255, 255, 0.1)'};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: #ffd700;\n    transform: scale(1.05);\n  }\n`;\n_c16 = EquipmentSlot;\nconst SoulRingsSection = styled.div`\n  margin-top: 30px;\n`;\n_c17 = SoulRingsSection;\nconst SoulRingsList = styled.div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n`;\n_c18 = SoulRingsList;\nconst SoulRing = styled.div`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  border: 3px solid;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  color: #fff;\n  \n  ${props => {\n  switch (props.ringType) {\n    case 'white':\n      return 'border-color: #fff; background: rgba(255, 255, 255, 0.2);';\n    case 'yellow':\n      return 'border-color: #ffd700; background: rgba(255, 215, 0, 0.2);';\n    case 'purple':\n      return 'border-color: #9b59b6; background: rgba(155, 89, 182, 0.2);';\n    case 'black':\n      return 'border-color: #2c3e50; background: rgba(44, 62, 80, 0.2);';\n    case 'red':\n      return 'border-color: #e74c3c; background: rgba(231, 76, 60, 0.2);';\n    default:\n      return 'border-color: #666; background: rgba(102, 102, 102, 0.2);';\n  }\n}}\n`;\n_c19 = SoulRing;\nconst EmptyRingSlot = styled.div`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  border: 2px dashed #666;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #666;\n`;\n_c20 = EmptyRingSlot;\nconst CharacterPanel = () => {\n  _s();\n  const {\n    state,\n    dispatch\n  } = useGame();\n  const {\n    currentCharacter\n  } = state;\n  const handleBack = () => {\n    dispatch({\n      type: 'SET_SCENE',\n      payload: 'main'\n    });\n  };\n  if (!currentCharacter) {\n    return /*#__PURE__*/_jsxDEV(PanelContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: \"\\u89D2\\u8272\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: handleBack,\n          children: \"\\u8FD4\\u56DE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#fff',\n          textAlign: 'center',\n          marginTop: '50px'\n        },\n        children: \"\\u6CA1\\u6709\\u9009\\u62E9\\u89D2\\u8272\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this);\n  }\n  const expPercentage = currentCharacter.exp / currentCharacter.maxExp * 100;\n  const hpPercentage = currentCharacter.hp / currentCharacter.maxHp * 100;\n  const mpPercentage = currentCharacter.mp / currentCharacter.maxMp * 100;\n  const equipmentSlots = [{\n    type: 'weapon',\n    name: '武器',\n    icon: '⚔️'\n  }, {\n    type: 'armor',\n    name: '护甲',\n    icon: '🛡️'\n  }, {\n    type: 'accessory',\n    name: '饰品',\n    icon: '💍'\n  }];\n\n  // 生成9个魂环槽位（斗罗大陆最多9个魂环）\n  const maxSoulRings = 9;\n  const soulRingSlots = Array.from({\n    length: maxSoulRings\n  }, (_, index) => {\n    const ring = currentCharacter.soulRings[index];\n    return ring || null;\n  });\n  return /*#__PURE__*/_jsxDEV(PanelContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\u89D2\\u8272\\u4FE1\\u606F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: handleBack,\n        children: \"\\u8FD4\\u56DE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CharacterDisplay, {\n      children: [/*#__PURE__*/_jsxDEV(CharacterInfo, {\n        children: [/*#__PURE__*/_jsxDEV(CharacterAvatar, {\n          children: currentCharacter.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CharacterName, {\n          children: currentCharacter.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatsList, {\n          children: [/*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"\\u7B49\\u7EA7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n              children: currentCharacter.level\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n                  children: \"\\u7ECF\\u9A8C\\u503C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                  children: [currentCharacter.exp, \"/\", currentCharacter.maxExp]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n                  percentage: expPercentage\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n                  children: \"\\u751F\\u547D\\u503C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                  children: [currentCharacter.hp, \"/\", currentCharacter.maxHp]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n                  percentage: hpPercentage,\n                  color: \"linear-gradient(90deg, #e74c3c, #c0392b)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n                  children: \"\\u9B42\\u529B\\u503C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n                  children: [currentCharacter.mp, \"/\", currentCharacter.maxMp]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n                children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n                  percentage: mpPercentage,\n                  color: \"linear-gradient(90deg, #3498db, #2980b9)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"\\u653B\\u51FB\\u529B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n              children: currentCharacter.attack\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"\\u9632\\u5FA1\\u529B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n              children: currentCharacter.defense\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"\\u901F\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n              children: currentCharacter.speed\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n            children: [/*#__PURE__*/_jsxDEV(StatLabel, {\n              children: \"\\u9B42\\u529B\\u7B49\\u7EA7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n              children: currentCharacter.soulPower\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EquipmentSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\u88C5\\u5907\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EquipmentGrid, {\n          children: equipmentSlots.map((slot, index) => {\n            const equipment = currentCharacter.equipment.find(eq => eq.type === slot.type);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(EquipmentSlot, {\n                hasItem: !!equipment,\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '30px'\n                  },\n                  children: equipment ? slot.icon : '❓'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#fff',\n                  fontSize: '12px',\n                  marginTop: '5px'\n                },\n                children: slot.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), equipment && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#ffd700',\n                  fontSize: '10px'\n                },\n                children: [equipment.name, \" +\", equipment.level]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SoulRingsSection, {\n          children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n            children: [\"\\u9B42\\u73AF (\", currentCharacter.soulRings.length, \"/\", maxSoulRings, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SoulRingsList, {\n            children: soulRingSlots.map((ring, index) => ring ? /*#__PURE__*/_jsxDEV(SoulRing, {\n              ringType: ring.type,\n              children: index + 1\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(EmptyRingSlot, {\n              children: index + 1\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 5\n  }, this);\n};\n_s(CharacterPanel, \"0Ezd+lYqYM4Ze1hEY5mBTpMQDZc=\", false, function () {\n  return [useGame];\n});\n_c21 = CharacterPanel;\nexport default CharacterPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21;\n$RefreshReg$(_c, \"PanelContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"BackButton\");\n$RefreshReg$(_c5, \"CharacterDisplay\");\n$RefreshReg$(_c6, \"CharacterInfo\");\n$RefreshReg$(_c7, \"CharacterAvatar\");\n$RefreshReg$(_c8, \"CharacterName\");\n$RefreshReg$(_c9, \"StatsList\");\n$RefreshReg$(_c0, \"StatItem\");\n$RefreshReg$(_c1, \"StatLabel\");\n$RefreshReg$(_c10, \"StatValue\");\n$RefreshReg$(_c11, \"ProgressBar\");\n$RefreshReg$(_c12, \"ProgressFill\");\n$RefreshReg$(_c13, \"EquipmentSection\");\n$RefreshReg$(_c14, \"SectionTitle\");\n$RefreshReg$(_c15, \"EquipmentGrid\");\n$RefreshReg$(_c16, \"EquipmentSlot\");\n$RefreshReg$(_c17, \"SoulRingsSection\");\n$RefreshReg$(_c18, \"SoulRingsList\");\n$RefreshReg$(_c19, \"SoulRing\");\n$RefreshReg$(_c20, \"EmptyRingSlot\");\n$RefreshReg$(_c21, \"CharacterPanel\");", "map": {"version": 3, "names": ["React", "styled", "useGame", "jsxDEV", "_jsxDEV", "PanelContainer", "div", "_c", "Header", "_c2", "Title", "h2", "_c3", "BackButton", "button", "_c4", "CharacterDisplay", "_c5", "CharacterInfo", "_c6", "<PERSON><PERSON><PERSON><PERSON>", "_c7", "CharacterName", "h3", "_c8", "StatsList", "_c9", "StatItem", "_c0", "StatLabel", "span", "_c1", "StatValue", "_c10", "ProgressBar", "_c11", "ProgressFill", "props", "percentage", "color", "_c12", "EquipmentSection", "_c13", "SectionTitle", "_c14", "EquipmentGrid", "_c15", "EquipmentSlot", "hasItem", "_c16", "SoulRingsSection", "_c17", "SoulRingsList", "_c18", "SoulRing", "ringType", "_c19", "EmptyRingSlot", "_c20", "CharacterPanel", "_s", "state", "dispatch", "currentCharacter", "handleBack", "type", "payload", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "textAlign", "marginTop", "expPercentage", "exp", "maxExp", "hpPercentage", "hp", "maxHp", "mpPercentage", "mp", "maxMp", "equipmentSlots", "name", "icon", "maxSoulRings", "soulRingSlots", "Array", "from", "length", "_", "index", "ring", "soulRings", "avatar", "level", "width", "display", "justifyContent", "attack", "defense", "speed", "soulPower", "map", "slot", "equipment", "find", "eq", "fontSize", "_c21", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/components/CharacterPanel.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\n\nconst PanelContainer = styled.div`\n  flex: 1;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n`;\n\nconst Title = styled.h2`\n  color: #ffd700;\n  margin: 0;\n  font-size: 2rem;\n`;\n\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #ffd700;\n  color: #ffd700;\n  padding: 10px 20px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n    transform: translateY(-2px);\n  }\n`;\n\nconst CharacterDisplay = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 30px;\n  flex: 1;\n`;\n\nconst CharacterInfo = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 15px;\n  padding: 30px;\n  border: 2px solid #ffd700;\n`;\n\nconst CharacterAvatar = styled.div`\n  width: 150px;\n  height: 150px;\n  border-radius: 50%;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 80px;\n  border: 5px solid #fff;\n  margin: 0 auto 20px;\n`;\n\nconst CharacterName = styled.h3`\n  color: #ffd700;\n  text-align: center;\n  font-size: 1.8rem;\n  margin-bottom: 20px;\n`;\n\nconst StatsList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n`;\n\nconst StatItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 8px;\n  border: 1px solid #ffd700;\n`;\n\nconst StatLabel = styled.span`\n  color: #fff;\n  font-weight: bold;\n`;\n\nconst StatValue = styled.span`\n  color: #ffd700;\n  font-weight: bold;\n  font-size: 1.1rem;\n`;\n\nconst ProgressBar = styled.div`\n  width: 100%;\n  height: 8px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 4px;\n  overflow: hidden;\n  margin-top: 5px;\n`;\n\nconst ProgressFill = styled.div<{ percentage: number; color?: string }>`\n  width: ${props => props.percentage}%;\n  height: 100%;\n  background: ${props => props.color || 'linear-gradient(90deg, #4ecdc4, #44a08d)'};\n  transition: width 0.3s ease;\n`;\n\nconst EquipmentSection = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 15px;\n  padding: 30px;\n  border: 2px solid #ffd700;\n`;\n\nconst SectionTitle = styled.h3`\n  color: #ffd700;\n  margin-bottom: 20px;\n  font-size: 1.5rem;\n`;\n\nconst EquipmentGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 15px;\n  margin-bottom: 30px;\n`;\n\nconst EquipmentSlot = styled.div<{ hasItem?: boolean }>`\n  width: 80px;\n  height: 80px;\n  border: 2px solid ${props => props.hasItem ? '#ffd700' : '#666'};\n  border-radius: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: ${props => props.hasItem ? 'rgba(255, 215, 0, 0.2)' : 'rgba(255, 255, 255, 0.1)'};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: #ffd700;\n    transform: scale(1.05);\n  }\n`;\n\nconst SoulRingsSection = styled.div`\n  margin-top: 30px;\n`;\n\nconst SoulRingsList = styled.div`\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n`;\n\nconst SoulRing = styled.div<{ ringType: string }>`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  border: 3px solid;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  color: #fff;\n  \n  ${props => {\n    switch (props.ringType) {\n      case 'white': return 'border-color: #fff; background: rgba(255, 255, 255, 0.2);';\n      case 'yellow': return 'border-color: #ffd700; background: rgba(255, 215, 0, 0.2);';\n      case 'purple': return 'border-color: #9b59b6; background: rgba(155, 89, 182, 0.2);';\n      case 'black': return 'border-color: #2c3e50; background: rgba(44, 62, 80, 0.2);';\n      case 'red': return 'border-color: #e74c3c; background: rgba(231, 76, 60, 0.2);';\n      default: return 'border-color: #666; background: rgba(102, 102, 102, 0.2);';\n    }\n  }}\n`;\n\nconst EmptyRingSlot = styled.div`\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  border: 2px dashed #666;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #666;\n`;\n\nconst CharacterPanel: React.FC = () => {\n  const { state, dispatch } = useGame();\n  const { currentCharacter } = state;\n\n  const handleBack = () => {\n    dispatch({ type: 'SET_SCENE', payload: 'main' });\n  };\n\n  if (!currentCharacter) {\n    return (\n      <PanelContainer>\n        <Header>\n          <Title>角色信息</Title>\n          <BackButton onClick={handleBack}>返回</BackButton>\n        </Header>\n        <div style={{ color: '#fff', textAlign: 'center', marginTop: '50px' }}>\n          没有选择角色\n        </div>\n      </PanelContainer>\n    );\n  }\n\n  const expPercentage = (currentCharacter.exp / currentCharacter.maxExp) * 100;\n  const hpPercentage = (currentCharacter.hp / currentCharacter.maxHp) * 100;\n  const mpPercentage = (currentCharacter.mp / currentCharacter.maxMp) * 100;\n\n  const equipmentSlots = [\n    { type: 'weapon', name: '武器', icon: '⚔️' },\n    { type: 'armor', name: '护甲', icon: '🛡️' },\n    { type: 'accessory', name: '饰品', icon: '💍' },\n  ];\n\n  // 生成9个魂环槽位（斗罗大陆最多9个魂环）\n  const maxSoulRings = 9;\n  const soulRingSlots = Array.from({ length: maxSoulRings }, (_, index) => {\n    const ring = currentCharacter.soulRings[index];\n    return ring || null;\n  });\n\n  return (\n    <PanelContainer>\n      <Header>\n        <Title>角色信息</Title>\n        <BackButton onClick={handleBack}>返回</BackButton>\n      </Header>\n\n      <CharacterDisplay>\n        <CharacterInfo>\n          <CharacterAvatar>{currentCharacter.avatar}</CharacterAvatar>\n          <CharacterName>{currentCharacter.name}</CharacterName>\n          \n          <StatsList>\n            <StatItem>\n              <StatLabel>等级</StatLabel>\n              <StatValue>{currentCharacter.level}</StatValue>\n            </StatItem>\n            \n            <StatItem>\n              <div style={{ width: '100%' }}>\n                <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <StatLabel>经验值</StatLabel>\n                  <StatValue>{currentCharacter.exp}/{currentCharacter.maxExp}</StatValue>\n                </div>\n                <ProgressBar>\n                  <ProgressFill percentage={expPercentage} />\n                </ProgressBar>\n              </div>\n            </StatItem>\n            \n            <StatItem>\n              <div style={{ width: '100%' }}>\n                <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <StatLabel>生命值</StatLabel>\n                  <StatValue>{currentCharacter.hp}/{currentCharacter.maxHp}</StatValue>\n                </div>\n                <ProgressBar>\n                  <ProgressFill percentage={hpPercentage} color=\"linear-gradient(90deg, #e74c3c, #c0392b)\" />\n                </ProgressBar>\n              </div>\n            </StatItem>\n            \n            <StatItem>\n              <div style={{ width: '100%' }}>\n                <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <StatLabel>魂力值</StatLabel>\n                  <StatValue>{currentCharacter.mp}/{currentCharacter.maxMp}</StatValue>\n                </div>\n                <ProgressBar>\n                  <ProgressFill percentage={mpPercentage} color=\"linear-gradient(90deg, #3498db, #2980b9)\" />\n                </ProgressBar>\n              </div>\n            </StatItem>\n            \n            <StatItem>\n              <StatLabel>攻击力</StatLabel>\n              <StatValue>{currentCharacter.attack}</StatValue>\n            </StatItem>\n            \n            <StatItem>\n              <StatLabel>防御力</StatLabel>\n              <StatValue>{currentCharacter.defense}</StatValue>\n            </StatItem>\n            \n            <StatItem>\n              <StatLabel>速度</StatLabel>\n              <StatValue>{currentCharacter.speed}</StatValue>\n            </StatItem>\n            \n            <StatItem>\n              <StatLabel>魂力等级</StatLabel>\n              <StatValue>{currentCharacter.soulPower}</StatValue>\n            </StatItem>\n          </StatsList>\n        </CharacterInfo>\n\n        <EquipmentSection>\n          <SectionTitle>装备</SectionTitle>\n          <EquipmentGrid>\n            {equipmentSlots.map((slot, index) => {\n              const equipment = currentCharacter.equipment.find(eq => eq.type === slot.type);\n              return (\n                <div key={index} style={{ textAlign: 'center' }}>\n                  <EquipmentSlot hasItem={!!equipment}>\n                    <span style={{ fontSize: '30px' }}>\n                      {equipment ? slot.icon : '❓'}\n                    </span>\n                  </EquipmentSlot>\n                  <div style={{ color: '#fff', fontSize: '12px', marginTop: '5px' }}>\n                    {slot.name}\n                  </div>\n                  {equipment && (\n                    <div style={{ color: '#ffd700', fontSize: '10px' }}>\n                      {equipment.name} +{equipment.level}\n                    </div>\n                  )}\n                </div>\n              );\n            })}\n          </EquipmentGrid>\n\n          <SoulRingsSection>\n            <SectionTitle>魂环 ({currentCharacter.soulRings.length}/{maxSoulRings})</SectionTitle>\n            <SoulRingsList>\n              {soulRingSlots.map((ring, index) => (\n                ring ? (\n                  <SoulRing key={index} ringType={ring.type}>\n                    {index + 1}\n                  </SoulRing>\n                ) : (\n                  <EmptyRingSlot key={index}>\n                    {index + 1}\n                  </EmptyRingSlot>\n                )\n              ))}\n            </SoulRingsList>\n          </SoulRingsSection>\n        </EquipmentSection>\n      </CharacterDisplay>\n    </PanelContainer>\n  );\n};\n\nexport default CharacterPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGJ,MAAM,CAACK,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,cAAc;AAOpB,MAAMG,MAAM,GAAGP,MAAM,CAACK,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,MAAM;AAOZ,MAAME,KAAK,GAAGT,MAAM,CAACU,EAAE;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,UAAU,GAAGZ,MAAM,CAACa,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,UAAU;AAgBhB,MAAMG,gBAAgB,GAAGf,MAAM,CAACK,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GALID,gBAAgB;AAOtB,MAAME,aAAa,GAAGjB,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GALID,aAAa;AAOnB,MAAME,eAAe,GAAGnB,MAAM,CAACK,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAXID,eAAe;AAarB,MAAME,aAAa,GAAGrB,MAAM,CAACsB,EAAE;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,aAAa;AAOnB,MAAMG,SAAS,GAAGxB,MAAM,CAACK,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,SAAS;AAMf,MAAME,QAAQ,GAAG1B,MAAM,CAACK,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GARID,QAAQ;AAUd,MAAME,SAAS,GAAG5B,MAAM,CAAC6B,IAAI;AAC7B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,SAAS;AAKf,MAAMG,SAAS,GAAG/B,MAAM,CAAC6B,IAAI;AAC7B;AACA;AACA;AACA,CAAC;AAACG,IAAA,GAJID,SAAS;AAMf,MAAME,WAAW,GAAGjC,MAAM,CAACK,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GAPID,WAAW;AASjB,MAAME,YAAY,GAAGnC,MAAM,CAACK,GAA2C;AACvE,WAAW+B,KAAK,IAAIA,KAAK,CAACC,UAAU;AACpC;AACA,gBAAgBD,KAAK,IAAIA,KAAK,CAACE,KAAK,IAAI,0CAA0C;AAClF;AACA,CAAC;AAACC,IAAA,GALIJ,YAAY;AAOlB,MAAMK,gBAAgB,GAAGxC,MAAM,CAACK,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GALID,gBAAgB;AAOtB,MAAME,YAAY,GAAG1C,MAAM,CAACsB,EAAE;AAC9B;AACA;AACA;AACA,CAAC;AAACqB,IAAA,GAJID,YAAY;AAMlB,MAAME,aAAa,GAAG5C,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GALID,aAAa;AAOnB,MAAME,aAAa,GAAG9C,MAAM,CAACK,GAA0B;AACvD;AACA;AACA,sBAAsB+B,KAAK,IAAIA,KAAK,CAACW,OAAO,GAAG,SAAS,GAAG,MAAM;AACjE;AACA;AACA;AACA;AACA,gBAAgBX,KAAK,IAAIA,KAAK,CAACW,OAAO,GAAG,wBAAwB,GAAG,0BAA0B;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAhBIF,aAAa;AAkBnB,MAAMG,gBAAgB,GAAGjD,MAAM,CAACK,GAAG;AACnC;AACA,CAAC;AAAC6C,IAAA,GAFID,gBAAgB;AAItB,MAAME,aAAa,GAAGnD,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAAC+C,IAAA,GAJID,aAAa;AAMnB,MAAME,QAAQ,GAAGrD,MAAM,CAACK,GAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI+B,KAAK,IAAI;EACT,QAAQA,KAAK,CAACkB,QAAQ;IACpB,KAAK,OAAO;MAAE,OAAO,2DAA2D;IAChF,KAAK,QAAQ;MAAE,OAAO,4DAA4D;IAClF,KAAK,QAAQ;MAAE,OAAO,6DAA6D;IACnF,KAAK,OAAO;MAAE,OAAO,2DAA2D;IAChF,KAAK,KAAK;MAAE,OAAO,4DAA4D;IAC/E;MAAS,OAAO,2DAA2D;EAC7E;AACF,CAAC;AACH,CAAC;AAACC,IAAA,GArBIF,QAAQ;AAuBd,MAAMG,aAAa,GAAGxD,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoD,IAAA,GATID,aAAa;AAWnB,MAAME,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAG5D,OAAO,CAAC,CAAC;EACrC,MAAM;IAAE6D;EAAiB,CAAC,GAAGF,KAAK;EAElC,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvBF,QAAQ,CAAC;MAAEG,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAO,CAAC,CAAC;EAClD,CAAC;EAED,IAAI,CAACH,gBAAgB,EAAE;IACrB,oBACE3D,OAAA,CAACC,cAAc;MAAA8D,QAAA,gBACb/D,OAAA,CAACI,MAAM;QAAA2D,QAAA,gBACL/D,OAAA,CAACM,KAAK;UAAAyD,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnBnE,OAAA,CAACS,UAAU;UAAC2D,OAAO,EAAER,UAAW;UAAAG,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACTnE,OAAA;QAAKqE,KAAK,EAAE;UAAElC,KAAK,EAAE,MAAM;UAAEmC,SAAS,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAEvE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAErB;EAEA,MAAMK,aAAa,GAAIb,gBAAgB,CAACc,GAAG,GAAGd,gBAAgB,CAACe,MAAM,GAAI,GAAG;EAC5E,MAAMC,YAAY,GAAIhB,gBAAgB,CAACiB,EAAE,GAAGjB,gBAAgB,CAACkB,KAAK,GAAI,GAAG;EACzE,MAAMC,YAAY,GAAInB,gBAAgB,CAACoB,EAAE,GAAGpB,gBAAgB,CAACqB,KAAK,GAAI,GAAG;EAEzE,MAAMC,cAAc,GAAG,CACrB;IAAEpB,IAAI,EAAE,QAAQ;IAAEqB,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC1C;IAAEtB,IAAI,EAAE,OAAO;IAAEqB,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1C;IAAEtB,IAAI,EAAE,WAAW;IAAEqB,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAK,CAAC,CAC9C;;EAED;EACA,MAAMC,YAAY,GAAG,CAAC;EACtB,MAAMC,aAAa,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAEJ;EAAa,CAAC,EAAE,CAACK,CAAC,EAAEC,KAAK,KAAK;IACvE,MAAMC,IAAI,GAAGhC,gBAAgB,CAACiC,SAAS,CAACF,KAAK,CAAC;IAC9C,OAAOC,IAAI,IAAI,IAAI;EACrB,CAAC,CAAC;EAEF,oBACE3F,OAAA,CAACC,cAAc;IAAA8D,QAAA,gBACb/D,OAAA,CAACI,MAAM;MAAA2D,QAAA,gBACL/D,OAAA,CAACM,KAAK;QAAAyD,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnBnE,OAAA,CAACS,UAAU;QAAC2D,OAAO,EAAER,UAAW;QAAAG,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAETnE,OAAA,CAACY,gBAAgB;MAAAmD,QAAA,gBACf/D,OAAA,CAACc,aAAa;QAAAiD,QAAA,gBACZ/D,OAAA,CAACgB,eAAe;UAAA+C,QAAA,EAAEJ,gBAAgB,CAACkC;QAAM;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,eAC5DnE,OAAA,CAACkB,aAAa;UAAA6C,QAAA,EAAEJ,gBAAgB,CAACuB;QAAI;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAEtDnE,OAAA,CAACqB,SAAS;UAAA0C,QAAA,gBACR/D,OAAA,CAACuB,QAAQ;YAAAwC,QAAA,gBACP/D,OAAA,CAACyB,SAAS;cAAAsC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzBnE,OAAA,CAAC4B,SAAS;cAAAmC,QAAA,EAAEJ,gBAAgB,CAACmC;YAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eAEXnE,OAAA,CAACuB,QAAQ;YAAAwC,QAAA,eACP/D,OAAA;cAAKqE,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAO,CAAE;cAAAhC,QAAA,gBAC5B/D,OAAA;gBAAKqE,KAAK,EAAE;kBAAE2B,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE;gBAAgB,CAAE;gBAAAlC,QAAA,gBAC/D/D,OAAA,CAACyB,SAAS;kBAAAsC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC1BnE,OAAA,CAAC4B,SAAS;kBAAAmC,QAAA,GAAEJ,gBAAgB,CAACc,GAAG,EAAC,GAAC,EAACd,gBAAgB,CAACe,MAAM;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNnE,OAAA,CAAC8B,WAAW;gBAAAiC,QAAA,eACV/D,OAAA,CAACgC,YAAY;kBAACE,UAAU,EAAEsC;gBAAc;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEXnE,OAAA,CAACuB,QAAQ;YAAAwC,QAAA,eACP/D,OAAA;cAAKqE,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAO,CAAE;cAAAhC,QAAA,gBAC5B/D,OAAA;gBAAKqE,KAAK,EAAE;kBAAE2B,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE;gBAAgB,CAAE;gBAAAlC,QAAA,gBAC/D/D,OAAA,CAACyB,SAAS;kBAAAsC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC1BnE,OAAA,CAAC4B,SAAS;kBAAAmC,QAAA,GAAEJ,gBAAgB,CAACiB,EAAE,EAAC,GAAC,EAACjB,gBAAgB,CAACkB,KAAK;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNnE,OAAA,CAAC8B,WAAW;gBAAAiC,QAAA,eACV/D,OAAA,CAACgC,YAAY;kBAACE,UAAU,EAAEyC,YAAa;kBAACxC,KAAK,EAAC;gBAA0C;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEXnE,OAAA,CAACuB,QAAQ;YAAAwC,QAAA,eACP/D,OAAA;cAAKqE,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAO,CAAE;cAAAhC,QAAA,gBAC5B/D,OAAA;gBAAKqE,KAAK,EAAE;kBAAE2B,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE;gBAAgB,CAAE;gBAAAlC,QAAA,gBAC/D/D,OAAA,CAACyB,SAAS;kBAAAsC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC1BnE,OAAA,CAAC4B,SAAS;kBAAAmC,QAAA,GAAEJ,gBAAgB,CAACoB,EAAE,EAAC,GAAC,EAACpB,gBAAgB,CAACqB,KAAK;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC,eACNnE,OAAA,CAAC8B,WAAW;gBAAAiC,QAAA,eACV/D,OAAA,CAACgC,YAAY;kBAACE,UAAU,EAAE4C,YAAa;kBAAC3C,KAAK,EAAC;gBAA0C;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEXnE,OAAA,CAACuB,QAAQ;YAAAwC,QAAA,gBACP/D,OAAA,CAACyB,SAAS;cAAAsC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1BnE,OAAA,CAAC4B,SAAS;cAAAmC,QAAA,EAAEJ,gBAAgB,CAACuC;YAAM;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAEXnE,OAAA,CAACuB,QAAQ;YAAAwC,QAAA,gBACP/D,OAAA,CAACyB,SAAS;cAAAsC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1BnE,OAAA,CAAC4B,SAAS;cAAAmC,QAAA,EAAEJ,gBAAgB,CAACwC;YAAO;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAEXnE,OAAA,CAACuB,QAAQ;YAAAwC,QAAA,gBACP/D,OAAA,CAACyB,SAAS;cAAAsC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzBnE,OAAA,CAAC4B,SAAS;cAAAmC,QAAA,EAAEJ,gBAAgB,CAACyC;YAAK;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eAEXnE,OAAA,CAACuB,QAAQ;YAAAwC,QAAA,gBACP/D,OAAA,CAACyB,SAAS;cAAAsC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BnE,OAAA,CAAC4B,SAAS;cAAAmC,QAAA,EAAEJ,gBAAgB,CAAC0C;YAAS;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEhBnE,OAAA,CAACqC,gBAAgB;QAAA0B,QAAA,gBACf/D,OAAA,CAACuC,YAAY;UAAAwB,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAC/BnE,OAAA,CAACyC,aAAa;UAAAsB,QAAA,EACXkB,cAAc,CAACqB,GAAG,CAAC,CAACC,IAAI,EAAEb,KAAK,KAAK;YACnC,MAAMc,SAAS,GAAG7C,gBAAgB,CAAC6C,SAAS,CAACC,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC7C,IAAI,KAAK0C,IAAI,CAAC1C,IAAI,CAAC;YAC9E,oBACE7D,OAAA;cAAiBqE,KAAK,EAAE;gBAAEC,SAAS,EAAE;cAAS,CAAE;cAAAP,QAAA,gBAC9C/D,OAAA,CAAC2C,aAAa;gBAACC,OAAO,EAAE,CAAC,CAAC4D,SAAU;gBAAAzC,QAAA,eAClC/D,OAAA;kBAAMqE,KAAK,EAAE;oBAAEsC,QAAQ,EAAE;kBAAO,CAAE;kBAAA5C,QAAA,EAC/ByC,SAAS,GAAGD,IAAI,CAACpB,IAAI,GAAG;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eAChBnE,OAAA;gBAAKqE,KAAK,EAAE;kBAAElC,KAAK,EAAE,MAAM;kBAAEwE,QAAQ,EAAE,MAAM;kBAAEpC,SAAS,EAAE;gBAAM,CAAE;gBAAAR,QAAA,EAC/DwC,IAAI,CAACrB;cAAI;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,EACLqC,SAAS,iBACRxG,OAAA;gBAAKqE,KAAK,EAAE;kBAAElC,KAAK,EAAE,SAAS;kBAAEwE,QAAQ,EAAE;gBAAO,CAAE;gBAAA5C,QAAA,GAChDyC,SAAS,CAACtB,IAAI,EAAC,IAAE,EAACsB,SAAS,CAACV,KAAK;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CACN;YAAA,GAbOuB,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eAEhBnE,OAAA,CAAC8C,gBAAgB;UAAAiB,QAAA,gBACf/D,OAAA,CAACuC,YAAY;YAAAwB,QAAA,GAAC,gBAAI,EAACJ,gBAAgB,CAACiC,SAAS,CAACJ,MAAM,EAAC,GAAC,EAACJ,YAAY,EAAC,GAAC;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACpFnE,OAAA,CAACgD,aAAa;YAAAe,QAAA,EACXsB,aAAa,CAACiB,GAAG,CAAC,CAACX,IAAI,EAAED,KAAK,KAC7BC,IAAI,gBACF3F,OAAA,CAACkD,QAAQ;cAAaC,QAAQ,EAAEwC,IAAI,CAAC9B,IAAK;cAAAE,QAAA,EACvC2B,KAAK,GAAG;YAAC,GADGA,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAAC,gBAEXnE,OAAA,CAACqD,aAAa;cAAAU,QAAA,EACX2B,KAAK,GAAG;YAAC,GADQA,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAElB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAErB,CAAC;AAACX,EAAA,CAhKID,cAAwB;EAAA,QACAzD,OAAO;AAAA;AAAA8G,IAAA,GAD/BrD,cAAwB;AAkK9B,eAAeA,cAAc;AAAC,IAAApD,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAK,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAsD,IAAA;AAAAC,YAAA,CAAA1G,EAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}