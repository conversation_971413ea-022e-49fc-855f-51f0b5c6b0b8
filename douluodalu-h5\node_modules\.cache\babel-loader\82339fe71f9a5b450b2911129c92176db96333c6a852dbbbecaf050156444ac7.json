{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\components\\\\GachaPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { useGame } from '../context/GameContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst glow = keyframes`\n  0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }\n  50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }\n  100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }\n`;\nconst cardFlip = keyframes`\n  0% { transform: rotateY(0deg); }\n  50% { transform: rotateY(90deg); }\n  100% { transform: rotateY(0deg); }\n`;\nconst GachaContainer = styled.div`\n  flex: 1;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n`;\n_c = GachaContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n`;\n_c2 = Header;\nconst Title = styled.h2`\n  color: #ffd700;\n  margin: 0;\n`;\n_c3 = Title;\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #ffd700;\n  color: #ffd700;\n  padding: 10px 20px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n  }\n`;\n_c4 = BackButton;\nconst GachaSection = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n  margin-bottom: 30px;\n`;\n_c5 = GachaSection;\nconst GachaBox = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid #ffd700;\n  border-radius: 15px;\n  padding: 30px;\n  text-align: center;\n`;\n_c6 = GachaBox;\nconst GachaTitle = styled.h3`\n  color: #ffd700;\n  margin-bottom: 15px;\n  font-size: 1.5rem;\n`;\n_c7 = GachaTitle;\nconst GachaDescription = styled.p`\n  color: #fff;\n  margin-bottom: 20px;\n  opacity: 0.9;\n`;\n_c8 = GachaDescription;\nconst ProbabilityList = styled.div`\n  margin-bottom: 20px;\n  text-align: left;\n`;\n_c9 = ProbabilityList;\nconst ProbabilityItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 5px;\n  color: ${props => {\n  switch (props.rarity) {\n    case 'legendary':\n      return '#ff6b6b';\n    case 'epic':\n      return '#9b59b6';\n    case 'rare':\n      return '#3498db';\n    default:\n      return '#fff';\n  }\n}};\n`;\n_c0 = ProbabilityItem;\nconst GachaButton = styled.button`\n  width: 100%;\n  padding: 15px;\n  margin-bottom: 10px;\n  border: none;\n  border-radius: 10px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n  if (!props.canAfford) {\n    return `\n        background: rgba(255, 255, 255, 0.1);\n        color: #666;\n        cursor: not-allowed;\n      `;\n  }\n  switch (props.variant) {\n    case 'ten':\n      return `\n          background: linear-gradient(45deg, #9b59b6, #8e44ad);\n          color: white;\n          &:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);\n          }\n        `;\n    default:\n      return `\n          background: linear-gradient(45deg, #3498db, #2980b9);\n          color: white;\n          &:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);\n          }\n        `;\n  }\n}}\n`;\n_c1 = GachaButton;\nconst ResultsContainer = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 15px;\n  padding: 20px;\n  border: 2px solid #ffd700;\n`;\n_c10 = ResultsContainer;\nconst ResultsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 15px;\n`;\n_c11 = ResultsGrid;\nconst ResultCard = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid;\n  border-radius: 10px;\n  padding: 15px;\n  text-align: center;\n  position: relative;\n  animation: ${props => props.isNew ? cardFlip : 'none'} 0.6s ease-in-out;\n  \n  ${props => {\n  switch (props.rarity) {\n    case 'legendary':\n      return `\n          border-color: #ff6b6b;\n          background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(231, 76, 60, 0.1));\n          animation: ${props.isNew ? `${cardFlip} 0.6s ease-in-out, ${glow} 2s infinite` : 'none'};\n        `;\n    case 'epic':\n      return `\n          border-color: #9b59b6;\n          background: linear-gradient(135deg, rgba(155, 89, 182, 0.2), rgba(142, 68, 173, 0.1));\n        `;\n    case 'rare':\n      return `\n          border-color: #3498db;\n          background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.1));\n        `;\n    default:\n      return `\n          border-color: #95a5a6;\n          background: rgba(149, 165, 166, 0.1);\n        `;\n  }\n}}\n`;\n_c12 = ResultCard;\nconst CharacterAvatar = styled.div`\n  font-size: 40px;\n  margin-bottom: 10px;\n`;\n_c13 = CharacterAvatar;\nconst CharacterName = styled.div`\n  color: #ffd700;\n  font-weight: bold;\n  margin-bottom: 5px;\n`;\n_c14 = CharacterName;\nconst CharacterRarity = styled.div`\n  font-size: 12px;\n  color: ${props => {\n  switch (props.rarity) {\n    case 'legendary':\n      return '#ff6b6b';\n    case 'epic':\n      return '#9b59b6';\n    case 'rare':\n      return '#3498db';\n    default:\n      return '#95a5a6';\n  }\n}};\n`;\n_c15 = CharacterRarity;\nconst NewBadge = styled.div`\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  background: #e74c3c;\n  color: white;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n`;\n\n// 角色池\n_c16 = NewBadge;\nconst characterPool = [\n// 普通角色 (70%)\n{\n  id: 'char_common_1',\n  name: '村民甲',\n  rarity: 'common',\n  avatar: '👨',\n  stats: {\n    attack: 10,\n    defense: 8\n  }\n}, {\n  id: 'char_common_2',\n  name: '村民乙',\n  rarity: 'common',\n  avatar: '👩',\n  stats: {\n    attack: 12,\n    defense: 6\n  }\n}, {\n  id: 'char_common_3',\n  name: '士兵',\n  rarity: 'common',\n  avatar: '🛡️',\n  stats: {\n    attack: 15,\n    defense: 12\n  }\n},\n// 稀有角色 (25%)\n{\n  id: 'char_rare_1',\n  name: '小舞',\n  rarity: 'rare',\n  avatar: '🐰',\n  stats: {\n    attack: 25,\n    defense: 18\n  }\n}, {\n  id: 'char_rare_2',\n  name: '宁荣荣',\n  rarity: 'rare',\n  avatar: '👸',\n  stats: {\n    attack: 20,\n    defense: 15\n  }\n}, {\n  id: 'char_rare_3',\n  name: '朱竹清',\n  rarity: 'rare',\n  avatar: '🐱',\n  stats: {\n    attack: 30,\n    defense: 20\n  }\n},\n// 史诗角色 (4%)\n{\n  id: 'char_epic_1',\n  name: '戴沐白',\n  rarity: 'epic',\n  avatar: '🦁',\n  stats: {\n    attack: 40,\n    defense: 30\n  }\n}, {\n  id: 'char_epic_2',\n  name: '马红俊',\n  rarity: 'epic',\n  avatar: '🔥',\n  stats: {\n    attack: 45,\n    defense: 25\n  }\n},\n// 传说角色 (1%)\n{\n  id: 'char_legendary_1',\n  name: '唐三',\n  rarity: 'legendary',\n  avatar: '🧙‍♂️',\n  stats: {\n    attack: 60,\n    defense: 40\n  }\n}];\nconst GachaPanel = () => {\n  _s();\n  const {\n    state,\n    dispatch\n  } = useGame();\n  const [gachaResults, setGachaResults] = useState([]);\n  const [isAnimating, setIsAnimating] = useState(false);\n  const handleBack = () => {\n    dispatch({\n      type: 'SET_SCENE',\n      payload: 'main'\n    });\n  };\n  const getRandomCharacter = () => {\n    const rand = Math.random() * 100;\n    if (rand < 1) {\n      // 1% 传说\n      return characterPool.filter(c => c.rarity === 'legendary')[0];\n    } else if (rand < 5) {\n      // 4% 史诗\n      const epics = characterPool.filter(c => c.rarity === 'epic');\n      return epics[Math.floor(Math.random() * epics.length)];\n    } else if (rand < 30) {\n      // 25% 稀有\n      const rares = characterPool.filter(c => c.rarity === 'rare');\n      return rares[Math.floor(Math.random() * rares.length)];\n    } else {\n      // 70% 普通\n      const commons = characterPool.filter(c => c.rarity === 'common');\n      return commons[Math.floor(Math.random() * commons.length)];\n    }\n  };\n  const performGacha = (count, cost) => {\n    if (cost.diamonds && state.user.diamonds < cost.diamonds) {\n      alert('钻石不足！');\n      return;\n    }\n    if (cost.coins && state.user.coins < cost.coins) {\n      alert('金币不足！');\n      return;\n    }\n    setIsAnimating(true);\n\n    // 扣除费用\n    const currencyUpdate = {};\n    if (cost.diamonds) currencyUpdate.diamonds = -cost.diamonds;\n    if (cost.coins) currencyUpdate.coins = -cost.coins;\n    dispatch({\n      type: 'UPDATE_CURRENCY',\n      payload: currencyUpdate\n    });\n\n    // 生成结果\n    const results = [];\n    for (let i = 0; i < count; i++) {\n      const character = getRandomCharacter();\n      results.push({\n        ...character,\n        id: `${character.id}_${Date.now()}_${i}`,\n        isNew: true\n      });\n    }\n\n    // 延迟显示结果以配合动画\n    setTimeout(() => {\n      setGachaResults(results);\n      setIsAnimating(false);\n\n      // 添加角色到队伍\n      results.forEach(result => {\n        const newCharacter = {\n          id: result.id,\n          name: result.name,\n          level: 1,\n          exp: 0,\n          maxExp: 100,\n          hp: 100 + result.stats.defense * 2,\n          maxHp: 100 + result.stats.defense * 2,\n          mp: 50,\n          maxMp: 50,\n          attack: result.stats.attack,\n          defense: result.stats.defense,\n          speed: 20,\n          soulPower: 1,\n          soulRings: [],\n          equipment: [],\n          avatar: result.avatar\n        };\n        dispatch({\n          type: 'ADD_CHARACTER',\n          payload: newCharacter\n        });\n      });\n\n      // 清除新标记\n      setTimeout(() => {\n        setGachaResults(prev => prev.map(r => ({\n          ...r,\n          isNew: false\n        })));\n      }, 2000);\n    }, 1000);\n  };\n  const canAffordSingle = state.user.diamonds >= 100;\n  const canAffordTen = state.user.diamonds >= 900;\n  return /*#__PURE__*/_jsxDEV(GachaContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\u9B42\\u5E08\\u53EC\\u5524\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: handleBack,\n        children: \"\\u8FD4\\u56DE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(GachaSection, {\n      children: [/*#__PURE__*/_jsxDEV(GachaBox, {\n        children: [/*#__PURE__*/_jsxDEV(GachaTitle, {\n          children: \"\\u666E\\u901A\\u53EC\\u5524\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(GachaDescription, {\n          children: \"\\u4F7F\\u7528\\u94BB\\u77F3\\u53EC\\u5524\\u5F3A\\u5927\\u7684\\u9B42\\u5E08\\u4F19\\u4F34\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProbabilityList, {\n          children: [/*#__PURE__*/_jsxDEV(ProbabilityItem, {\n            rarity: \"legendary\",\n            children: \"\\u4F20\\u8BF4\\u89D2\\u8272: 1%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProbabilityItem, {\n            rarity: \"epic\",\n            children: \"\\u53F2\\u8BD7\\u89D2\\u8272: 4%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProbabilityItem, {\n            rarity: \"rare\",\n            children: \"\\u7A00\\u6709\\u89D2\\u8272: 25%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProbabilityItem, {\n            rarity: \"common\",\n            children: \"\\u666E\\u901A\\u89D2\\u8272: 70%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(GachaButton, {\n          variant: \"single\",\n          canAfford: canAffordSingle,\n          onClick: () => performGacha(1, {\n            diamonds: 100\n          }),\n          disabled: !canAffordSingle || isAnimating,\n          children: \"\\u5355\\u6B21\\u53EC\\u5524 (\\uD83D\\uDC8E100)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(GachaButton, {\n          variant: \"ten\",\n          canAfford: canAffordTen,\n          onClick: () => performGacha(10, {\n            diamonds: 900\n          }),\n          disabled: !canAffordTen || isAnimating,\n          children: \"\\u5341\\u8FDE\\u53EC\\u5524 (\\uD83D\\uDC8E900) \\u4F18\\u60E0!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(GachaBox, {\n        children: [/*#__PURE__*/_jsxDEV(GachaTitle, {\n          children: \"\\u53CB\\u60C5\\u53EC\\u5524\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(GachaDescription, {\n          children: \"\\u4F7F\\u7528\\u91D1\\u5E01\\u8FDB\\u884C\\u53CB\\u60C5\\u53EC\\u5524\\uFF0C\\u83B7\\u5F97\\u57FA\\u7840\\u89D2\\u8272\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProbabilityList, {\n          children: [/*#__PURE__*/_jsxDEV(ProbabilityItem, {\n            rarity: \"rare\",\n            children: \"\\u7A00\\u6709\\u89D2\\u8272: 5%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ProbabilityItem, {\n            rarity: \"common\",\n            children: \"\\u666E\\u901A\\u89D2\\u8272: 95%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(GachaButton, {\n          variant: \"single\",\n          canAfford: state.user.coins >= 1000,\n          onClick: () => performGacha(1, {\n            coins: 1000\n          }),\n          disabled: state.user.coins < 1000 || isAnimating,\n          children: \"\\u53CB\\u60C5\\u53EC\\u5524 (\\uD83E\\uDE991000)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), gachaResults.length > 0 && /*#__PURE__*/_jsxDEV(ResultsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#ffd700',\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: \"\\u53EC\\u5524\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ResultsGrid, {\n        children: gachaResults.map((result, index) => /*#__PURE__*/_jsxDEV(ResultCard, {\n          rarity: result.rarity,\n          isNew: result.isNew,\n          children: [result.isNew && /*#__PURE__*/_jsxDEV(NewBadge, {\n            children: \"!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 34\n          }, this), /*#__PURE__*/_jsxDEV(CharacterAvatar, {\n            children: result.avatar\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CharacterName, {\n            children: result.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CharacterRarity, {\n            rarity: result.rarity,\n            children: result.rarity === 'legendary' ? '传说' : result.rarity === 'epic' ? '史诗' : result.rarity === 'rare' ? '稀有' : '普通'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 9\n    }, this), isAnimating && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        color: '#ffd700',\n        fontSize: '1.5rem',\n        margin: '50px 0'\n      },\n      children: \"\\u53EC\\u5524\\u4E2D...\\u2728\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 347,\n    columnNumber: 5\n  }, this);\n};\n_s(GachaPanel, \"Qi+v8Jrj90htJvS5aiDFIopBm9Y=\", false, function () {\n  return [useGame];\n});\n_c17 = GachaPanel;\nexport default GachaPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"GachaContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"BackButton\");\n$RefreshReg$(_c5, \"GachaSection\");\n$RefreshReg$(_c6, \"GachaBox\");\n$RefreshReg$(_c7, \"GachaTitle\");\n$RefreshReg$(_c8, \"GachaDescription\");\n$RefreshReg$(_c9, \"ProbabilityList\");\n$RefreshReg$(_c0, \"ProbabilityItem\");\n$RefreshReg$(_c1, \"GachaButton\");\n$RefreshReg$(_c10, \"ResultsContainer\");\n$RefreshReg$(_c11, \"ResultsGrid\");\n$RefreshReg$(_c12, \"ResultCard\");\n$RefreshReg$(_c13, \"CharacterAvatar\");\n$RefreshReg$(_c14, \"CharacterName\");\n$RefreshReg$(_c15, \"CharacterRarity\");\n$RefreshReg$(_c16, \"NewBadge\");\n$RefreshReg$(_c17, \"GachaPanel\");", "map": {"version": 3, "names": ["React", "useState", "styled", "keyframes", "useGame", "jsxDEV", "_jsxDEV", "glow", "cardFlip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Header", "_c2", "Title", "h2", "_c3", "BackButton", "button", "_c4", "GachaSection", "_c5", "GachaBox", "_c6", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "h3", "_c7", "GachaDescription", "p", "_c8", "ProbabilityList", "_c9", "ProbabilityItem", "props", "rarity", "_c0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "can<PERSON>fford", "variant", "_c1", "ResultsContainer", "_c10", "ResultsGrid", "_c11", "ResultCard", "isNew", "_c12", "<PERSON><PERSON><PERSON><PERSON>", "_c13", "CharacterName", "_c14", "CharacterRarity", "_c15", "NewBadge", "_c16", "characterPool", "id", "name", "avatar", "stats", "attack", "defense", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "state", "dispatch", "gachaResults", "setGachaResults", "isAnimating", "setIsAnimating", "handleBack", "type", "payload", "getRandomCharacter", "rand", "Math", "random", "filter", "c", "epics", "floor", "length", "rares", "commons", "<PERSON><PERSON><PERSON><PERSON>", "count", "cost", "diamonds", "user", "alert", "coins", "currencyUpdate", "results", "i", "character", "push", "Date", "now", "setTimeout", "for<PERSON>ach", "result", "newCharacter", "level", "exp", "maxExp", "hp", "maxHp", "mp", "maxMp", "speed", "soulPower", "soulRings", "equipment", "prev", "map", "r", "canAffordSingle", "canAffordTen", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "style", "color", "marginBottom", "textAlign", "index", "fontSize", "margin", "_c17", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/components/GachaPanel.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { useGame } from '../context/GameContext';\n\nconst glow = keyframes`\n  0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }\n  50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }\n  100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }\n`;\n\nconst cardFlip = keyframes`\n  0% { transform: rotateY(0deg); }\n  50% { transform: rotateY(90deg); }\n  100% { transform: rotateY(0deg); }\n`;\n\nconst GachaContainer = styled.div`\n  flex: 1;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n`;\n\nconst Title = styled.h2`\n  color: #ffd700;\n  margin: 0;\n`;\n\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #ffd700;\n  color: #ffd700;\n  padding: 10px 20px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n  }\n`;\n\nconst GachaSection = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n  margin-bottom: 30px;\n`;\n\nconst GachaBox = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid #ffd700;\n  border-radius: 15px;\n  padding: 30px;\n  text-align: center;\n`;\n\nconst GachaTitle = styled.h3`\n  color: #ffd700;\n  margin-bottom: 15px;\n  font-size: 1.5rem;\n`;\n\nconst GachaDescription = styled.p`\n  color: #fff;\n  margin-bottom: 20px;\n  opacity: 0.9;\n`;\n\nconst ProbabilityList = styled.div`\n  margin-bottom: 20px;\n  text-align: left;\n`;\n\nconst ProbabilityItem = styled.div<{ rarity: string }>`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 5px;\n  color: ${props => {\n    switch (props.rarity) {\n      case 'legendary': return '#ff6b6b';\n      case 'epic': return '#9b59b6';\n      case 'rare': return '#3498db';\n      default: return '#fff';\n    }\n  }};\n`;\n\nconst GachaButton = styled.button<{ variant?: 'single' | 'ten'; canAfford?: boolean }>`\n  width: 100%;\n  padding: 15px;\n  margin-bottom: 10px;\n  border: none;\n  border-radius: 10px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n    if (!props.canAfford) {\n      return `\n        background: rgba(255, 255, 255, 0.1);\n        color: #666;\n        cursor: not-allowed;\n      `;\n    }\n    \n    switch (props.variant) {\n      case 'ten':\n        return `\n          background: linear-gradient(45deg, #9b59b6, #8e44ad);\n          color: white;\n          &:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);\n          }\n        `;\n      default:\n        return `\n          background: linear-gradient(45deg, #3498db, #2980b9);\n          color: white;\n          &:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);\n          }\n        `;\n    }\n  }}\n`;\n\nconst ResultsContainer = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 15px;\n  padding: 20px;\n  border: 2px solid #ffd700;\n`;\n\nconst ResultsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 15px;\n`;\n\nconst ResultCard = styled.div<{ rarity: string; isNew?: boolean }>`\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid;\n  border-radius: 10px;\n  padding: 15px;\n  text-align: center;\n  position: relative;\n  animation: ${props => props.isNew ? cardFlip : 'none'} 0.6s ease-in-out;\n  \n  ${props => {\n    switch (props.rarity) {\n      case 'legendary':\n        return `\n          border-color: #ff6b6b;\n          background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(231, 76, 60, 0.1));\n          animation: ${props.isNew ? `${cardFlip} 0.6s ease-in-out, ${glow} 2s infinite` : 'none'};\n        `;\n      case 'epic':\n        return `\n          border-color: #9b59b6;\n          background: linear-gradient(135deg, rgba(155, 89, 182, 0.2), rgba(142, 68, 173, 0.1));\n        `;\n      case 'rare':\n        return `\n          border-color: #3498db;\n          background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.1));\n        `;\n      default:\n        return `\n          border-color: #95a5a6;\n          background: rgba(149, 165, 166, 0.1);\n        `;\n    }\n  }}\n`;\n\nconst CharacterAvatar = styled.div`\n  font-size: 40px;\n  margin-bottom: 10px;\n`;\n\nconst CharacterName = styled.div`\n  color: #ffd700;\n  font-weight: bold;\n  margin-bottom: 5px;\n`;\n\nconst CharacterRarity = styled.div<{ rarity: string }>`\n  font-size: 12px;\n  color: ${props => {\n    switch (props.rarity) {\n      case 'legendary': return '#ff6b6b';\n      case 'epic': return '#9b59b6';\n      case 'rare': return '#3498db';\n      default: return '#95a5a6';\n    }\n  }};\n`;\n\nconst NewBadge = styled.div`\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  background: #e74c3c;\n  color: white;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  font-weight: bold;\n`;\n\n// 角色池\nconst characterPool = [\n  // 普通角色 (70%)\n  { id: 'char_common_1', name: '村民甲', rarity: 'common', avatar: '👨', stats: { attack: 10, defense: 8 } },\n  { id: 'char_common_2', name: '村民乙', rarity: 'common', avatar: '👩', stats: { attack: 12, defense: 6 } },\n  { id: 'char_common_3', name: '士兵', rarity: 'common', avatar: '🛡️', stats: { attack: 15, defense: 12 } },\n  \n  // 稀有角色 (25%)\n  { id: 'char_rare_1', name: '小舞', rarity: 'rare', avatar: '🐰', stats: { attack: 25, defense: 18 } },\n  { id: 'char_rare_2', name: '宁荣荣', rarity: 'rare', avatar: '👸', stats: { attack: 20, defense: 15 } },\n  { id: 'char_rare_3', name: '朱竹清', rarity: 'rare', avatar: '🐱', stats: { attack: 30, defense: 20 } },\n  \n  // 史诗角色 (4%)\n  { id: 'char_epic_1', name: '戴沐白', rarity: 'epic', avatar: '🦁', stats: { attack: 40, defense: 30 } },\n  { id: 'char_epic_2', name: '马红俊', rarity: 'epic', avatar: '🔥', stats: { attack: 45, defense: 25 } },\n  \n  // 传说角色 (1%)\n  { id: 'char_legendary_1', name: '唐三', rarity: 'legendary', avatar: '🧙‍♂️', stats: { attack: 60, defense: 40 } },\n];\n\nconst GachaPanel: React.FC = () => {\n  const { state, dispatch } = useGame();\n  const [gachaResults, setGachaResults] = useState<any[]>([]);\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  const handleBack = () => {\n    dispatch({ type: 'SET_SCENE', payload: 'main' });\n  };\n\n  const getRandomCharacter = () => {\n    const rand = Math.random() * 100;\n    \n    if (rand < 1) {\n      // 1% 传说\n      return characterPool.filter(c => c.rarity === 'legendary')[0];\n    } else if (rand < 5) {\n      // 4% 史诗\n      const epics = characterPool.filter(c => c.rarity === 'epic');\n      return epics[Math.floor(Math.random() * epics.length)];\n    } else if (rand < 30) {\n      // 25% 稀有\n      const rares = characterPool.filter(c => c.rarity === 'rare');\n      return rares[Math.floor(Math.random() * rares.length)];\n    } else {\n      // 70% 普通\n      const commons = characterPool.filter(c => c.rarity === 'common');\n      return commons[Math.floor(Math.random() * commons.length)];\n    }\n  };\n\n  const performGacha = (count: number, cost: { diamonds?: number; coins?: number }) => {\n    if (cost.diamonds && state.user.diamonds < cost.diamonds) {\n      alert('钻石不足！');\n      return;\n    }\n    if (cost.coins && state.user.coins < cost.coins) {\n      alert('金币不足！');\n      return;\n    }\n\n    setIsAnimating(true);\n    \n    // 扣除费用\n    const currencyUpdate: any = {};\n    if (cost.diamonds) currencyUpdate.diamonds = -cost.diamonds;\n    if (cost.coins) currencyUpdate.coins = -cost.coins;\n    dispatch({ type: 'UPDATE_CURRENCY', payload: currencyUpdate });\n\n    // 生成结果\n    const results = [];\n    for (let i = 0; i < count; i++) {\n      const character = getRandomCharacter();\n      results.push({\n        ...character,\n        id: `${character.id}_${Date.now()}_${i}`,\n        isNew: true,\n      });\n    }\n\n    // 延迟显示结果以配合动画\n    setTimeout(() => {\n      setGachaResults(results);\n      setIsAnimating(false);\n      \n      // 添加角色到队伍\n      results.forEach(result => {\n        const newCharacter = {\n          id: result.id,\n          name: result.name,\n          level: 1,\n          exp: 0,\n          maxExp: 100,\n          hp: 100 + (result.stats.defense * 2),\n          maxHp: 100 + (result.stats.defense * 2),\n          mp: 50,\n          maxMp: 50,\n          attack: result.stats.attack,\n          defense: result.stats.defense,\n          speed: 20,\n          soulPower: 1,\n          soulRings: [],\n          equipment: [],\n          avatar: result.avatar,\n        };\n        \n        dispatch({ type: 'ADD_CHARACTER', payload: newCharacter });\n      });\n      \n      // 清除新标记\n      setTimeout(() => {\n        setGachaResults(prev => prev.map(r => ({ ...r, isNew: false })));\n      }, 2000);\n      \n    }, 1000);\n  };\n\n  const canAffordSingle = state.user.diamonds >= 100;\n  const canAffordTen = state.user.diamonds >= 900;\n\n  return (\n    <GachaContainer>\n      <Header>\n        <Title>魂师召唤</Title>\n        <BackButton onClick={handleBack}>返回</BackButton>\n      </Header>\n\n      <GachaSection>\n        <GachaBox>\n          <GachaTitle>普通召唤</GachaTitle>\n          <GachaDescription>\n            使用钻石召唤强大的魂师伙伴！\n          </GachaDescription>\n          \n          <ProbabilityList>\n            <ProbabilityItem rarity=\"legendary\">传说角色: 1%</ProbabilityItem>\n            <ProbabilityItem rarity=\"epic\">史诗角色: 4%</ProbabilityItem>\n            <ProbabilityItem rarity=\"rare\">稀有角色: 25%</ProbabilityItem>\n            <ProbabilityItem rarity=\"common\">普通角色: 70%</ProbabilityItem>\n          </ProbabilityList>\n          \n          <GachaButton\n            variant=\"single\"\n            canAfford={canAffordSingle}\n            onClick={() => performGacha(1, { diamonds: 100 })}\n            disabled={!canAffordSingle || isAnimating}\n          >\n            单次召唤 (💎100)\n          </GachaButton>\n          \n          <GachaButton\n            variant=\"ten\"\n            canAfford={canAffordTen}\n            onClick={() => performGacha(10, { diamonds: 900 })}\n            disabled={!canAffordTen || isAnimating}\n          >\n            十连召唤 (💎900) 优惠!\n          </GachaButton>\n        </GachaBox>\n\n        <GachaBox>\n          <GachaTitle>友情召唤</GachaTitle>\n          <GachaDescription>\n            使用金币进行友情召唤，获得基础角色！\n          </GachaDescription>\n          \n          <ProbabilityList>\n            <ProbabilityItem rarity=\"rare\">稀有角色: 5%</ProbabilityItem>\n            <ProbabilityItem rarity=\"common\">普通角色: 95%</ProbabilityItem>\n          </ProbabilityList>\n          \n          <GachaButton\n            variant=\"single\"\n            canAfford={state.user.coins >= 1000}\n            onClick={() => performGacha(1, { coins: 1000 })}\n            disabled={state.user.coins < 1000 || isAnimating}\n          >\n            友情召唤 (🪙1000)\n          </GachaButton>\n        </GachaBox>\n      </GachaSection>\n\n      {gachaResults.length > 0 && (\n        <ResultsContainer>\n          <h3 style={{ color: '#ffd700', marginBottom: '20px', textAlign: 'center' }}>\n            召唤结果\n          </h3>\n          <ResultsGrid>\n            {gachaResults.map((result, index) => (\n              <ResultCard key={index} rarity={result.rarity} isNew={result.isNew}>\n                {result.isNew && <NewBadge>!</NewBadge>}\n                <CharacterAvatar>{result.avatar}</CharacterAvatar>\n                <CharacterName>{result.name}</CharacterName>\n                <CharacterRarity rarity={result.rarity}>\n                  {result.rarity === 'legendary' ? '传说' :\n                   result.rarity === 'epic' ? '史诗' :\n                   result.rarity === 'rare' ? '稀有' : '普通'}\n                </CharacterRarity>\n              </ResultCard>\n            ))}\n          </ResultsGrid>\n        </ResultsContainer>\n      )}\n\n      {isAnimating && (\n        <div style={{ \n          textAlign: 'center', \n          color: '#ffd700', \n          fontSize: '1.5rem', \n          margin: '50px 0' \n        }}>\n          召唤中...✨\n        </div>\n      )}\n    </GachaContainer>\n  );\n};\n\nexport default GachaPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,IAAI,GAAGJ,SAAS;AACtB;AACA;AACA;AACA,CAAC;AAED,MAAMK,QAAQ,GAAGL,SAAS;AAC1B;AACA;AACA;AACA,CAAC;AAED,MAAMM,cAAc,GAAGP,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,cAAc;AAOpB,MAAMG,MAAM,GAAGV,MAAM,CAACQ,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,MAAM;AAOZ,MAAME,KAAK,GAAGZ,MAAM,CAACa,EAAE;AACvB;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,UAAU,GAAGf,MAAM,CAACgB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,UAAU;AAehB,MAAMG,YAAY,GAAGlB,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GALID,YAAY;AAOlB,MAAME,QAAQ,GAAGpB,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GANID,QAAQ;AAQd,MAAME,UAAU,GAAGtB,MAAM,CAACuB,EAAE;AAC5B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,gBAAgB,GAAGzB,MAAM,CAAC0B,CAAC;AACjC;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,gBAAgB;AAMtB,MAAMG,eAAe,GAAG5B,MAAM,CAACQ,GAAG;AAClC;AACA;AACA,CAAC;AAACqB,GAAA,GAHID,eAAe;AAKrB,MAAME,eAAe,GAAG9B,MAAM,CAACQ,GAAuB;AACtD;AACA;AACA;AACA,WAAWuB,KAAK,IAAI;EAChB,QAAQA,KAAK,CAACC,MAAM;IAClB,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC,KAAK,MAAM;MAAE,OAAO,SAAS;IAC7B,KAAK,MAAM;MAAE,OAAO,SAAS;IAC7B;MAAS,OAAO,MAAM;EACxB;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GAZIH,eAAe;AAcrB,MAAMI,WAAW,GAAGlC,MAAM,CAACgB,MAA2D;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIe,KAAK,IAAI;EACT,IAAI,CAACA,KAAK,CAACI,SAAS,EAAE;IACpB,OAAO;AACb;AACA;AACA;AACA,OAAO;EACH;EAEA,QAAQJ,KAAK,CAACK,OAAO;IACnB,KAAK,KAAK;MACR,OAAO;AACf;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GAxCIH,WAAW;AA0CjB,MAAMI,gBAAgB,GAAGtC,MAAM,CAACQ,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GALID,gBAAgB;AAOtB,MAAME,WAAW,GAAGxC,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GAJID,WAAW;AAMjB,MAAME,UAAU,GAAG1C,MAAM,CAACQ,GAAwC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,eAAeuB,KAAK,IAAIA,KAAK,CAACY,KAAK,GAAGrC,QAAQ,GAAG,MAAM;AACvD;AACA,IAAIyB,KAAK,IAAI;EACT,QAAQA,KAAK,CAACC,MAAM;IAClB,KAAK,WAAW;MACd,OAAO;AACf;AACA;AACA,uBAAuBD,KAAK,CAACY,KAAK,GAAG,GAAGrC,QAAQ,sBAAsBD,IAAI,cAAc,GAAG,MAAM;AACjG,SAAS;IACH,KAAK,MAAM;MACT,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,MAAM;MACT,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACuC,IAAA,GAlCIF,UAAU;AAoChB,MAAMG,eAAe,GAAG7C,MAAM,CAACQ,GAAG;AAClC;AACA;AACA,CAAC;AAACsC,IAAA,GAHID,eAAe;AAKrB,MAAME,aAAa,GAAG/C,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACwC,IAAA,GAJID,aAAa;AAMnB,MAAME,eAAe,GAAGjD,MAAM,CAACQ,GAAuB;AACtD;AACA,WAAWuB,KAAK,IAAI;EAChB,QAAQA,KAAK,CAACC,MAAM;IAClB,KAAK,WAAW;MAAE,OAAO,SAAS;IAClC,KAAK,MAAM;MAAE,OAAO,SAAS;IAC7B,KAAK,MAAM;MAAE,OAAO,SAAS;IAC7B;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;AACH,CAAC;AAACkB,IAAA,GAVID,eAAe;AAYrB,MAAME,QAAQ,GAAGnD,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAA4C,IAAA,GAhBMD,QAAQ;AAiBd,MAAME,aAAa,GAAG;AACpB;AACA;EAAEC,EAAE,EAAE,eAAe;EAAEC,IAAI,EAAE,KAAK;EAAEvB,MAAM,EAAE,QAAQ;EAAEwB,MAAM,EAAE,IAAI;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAE;AAAE,CAAC,EACvG;EAAEL,EAAE,EAAE,eAAe;EAAEC,IAAI,EAAE,KAAK;EAAEvB,MAAM,EAAE,QAAQ;EAAEwB,MAAM,EAAE,IAAI;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAE;AAAE,CAAC,EACvG;EAAEL,EAAE,EAAE,eAAe;EAAEC,IAAI,EAAE,IAAI;EAAEvB,MAAM,EAAE,QAAQ;EAAEwB,MAAM,EAAE,KAAK;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG;AAAE,CAAC;AAExG;AACA;EAAEL,EAAE,EAAE,aAAa;EAAEC,IAAI,EAAE,IAAI;EAAEvB,MAAM,EAAE,MAAM;EAAEwB,MAAM,EAAE,IAAI;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG;AAAE,CAAC,EACnG;EAAEL,EAAE,EAAE,aAAa;EAAEC,IAAI,EAAE,KAAK;EAAEvB,MAAM,EAAE,MAAM;EAAEwB,MAAM,EAAE,IAAI;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG;AAAE,CAAC,EACpG;EAAEL,EAAE,EAAE,aAAa;EAAEC,IAAI,EAAE,KAAK;EAAEvB,MAAM,EAAE,MAAM;EAAEwB,MAAM,EAAE,IAAI;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG;AAAE,CAAC;AAEpG;AACA;EAAEL,EAAE,EAAE,aAAa;EAAEC,IAAI,EAAE,KAAK;EAAEvB,MAAM,EAAE,MAAM;EAAEwB,MAAM,EAAE,IAAI;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG;AAAE,CAAC,EACpG;EAAEL,EAAE,EAAE,aAAa;EAAEC,IAAI,EAAE,KAAK;EAAEvB,MAAM,EAAE,MAAM;EAAEwB,MAAM,EAAE,IAAI;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG;AAAE,CAAC;AAEpG;AACA;EAAEL,EAAE,EAAE,kBAAkB;EAAEC,IAAI,EAAE,IAAI;EAAEvB,MAAM,EAAE,WAAW;EAAEwB,MAAM,EAAE,OAAO;EAAEC,KAAK,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG;AAAE,CAAC,CACjH;AAED,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAG7D,OAAO,CAAC,CAAC;EACrC,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAQ,EAAE,CAAC;EAC3D,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMqE,UAAU,GAAGA,CAAA,KAAM;IACvBL,QAAQ,CAAC;MAAEM,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAO,CAAC,CAAC;EAClD,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,IAAI,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IAEhC,IAAIF,IAAI,GAAG,CAAC,EAAE;MACZ;MACA,OAAOnB,aAAa,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC,MAAM,IAAIwC,IAAI,GAAG,CAAC,EAAE;MACnB;MACA,MAAMK,KAAK,GAAGxB,aAAa,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,MAAM,KAAK,MAAM,CAAC;MAC5D,OAAO6C,KAAK,CAACJ,IAAI,CAACK,KAAK,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGG,KAAK,CAACE,MAAM,CAAC,CAAC;IACxD,CAAC,MAAM,IAAIP,IAAI,GAAG,EAAE,EAAE;MACpB;MACA,MAAMQ,KAAK,GAAG3B,aAAa,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,MAAM,KAAK,MAAM,CAAC;MAC5D,OAAOgD,KAAK,CAACP,IAAI,CAACK,KAAK,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGM,KAAK,CAACD,MAAM,CAAC,CAAC;IACxD,CAAC,MAAM;MACL;MACA,MAAME,OAAO,GAAG5B,aAAa,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5C,MAAM,KAAK,QAAQ,CAAC;MAChE,OAAOiD,OAAO,CAACR,IAAI,CAACK,KAAK,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGO,OAAO,CAACF,MAAM,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAMG,YAAY,GAAGA,CAACC,KAAa,EAAEC,IAA2C,KAAK;IACnF,IAAIA,IAAI,CAACC,QAAQ,IAAIvB,KAAK,CAACwB,IAAI,CAACD,QAAQ,GAAGD,IAAI,CAACC,QAAQ,EAAE;MACxDE,KAAK,CAAC,OAAO,CAAC;MACd;IACF;IACA,IAAIH,IAAI,CAACI,KAAK,IAAI1B,KAAK,CAACwB,IAAI,CAACE,KAAK,GAAGJ,IAAI,CAACI,KAAK,EAAE;MAC/CD,KAAK,CAAC,OAAO,CAAC;MACd;IACF;IAEApB,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACA,MAAMsB,cAAmB,GAAG,CAAC,CAAC;IAC9B,IAAIL,IAAI,CAACC,QAAQ,EAAEI,cAAc,CAACJ,QAAQ,GAAG,CAACD,IAAI,CAACC,QAAQ;IAC3D,IAAID,IAAI,CAACI,KAAK,EAAEC,cAAc,CAACD,KAAK,GAAG,CAACJ,IAAI,CAACI,KAAK;IAClDzB,QAAQ,CAAC;MAAEM,IAAI,EAAE,iBAAiB;MAAEC,OAAO,EAAEmB;IAAe,CAAC,CAAC;;IAE9D;IACA,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,EAAEQ,CAAC,EAAE,EAAE;MAC9B,MAAMC,SAAS,GAAGrB,kBAAkB,CAAC,CAAC;MACtCmB,OAAO,CAACG,IAAI,CAAC;QACX,GAAGD,SAAS;QACZtC,EAAE,EAAE,GAAGsC,SAAS,CAACtC,EAAE,IAAIwC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIJ,CAAC,EAAE;QACxChD,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;;IAEA;IACAqD,UAAU,CAAC,MAAM;MACf/B,eAAe,CAACyB,OAAO,CAAC;MACxBvB,cAAc,CAAC,KAAK,CAAC;;MAErB;MACAuB,OAAO,CAACO,OAAO,CAACC,MAAM,IAAI;QACxB,MAAMC,YAAY,GAAG;UACnB7C,EAAE,EAAE4C,MAAM,CAAC5C,EAAE;UACbC,IAAI,EAAE2C,MAAM,CAAC3C,IAAI;UACjB6C,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE,CAAC;UACNC,MAAM,EAAE,GAAG;UACXC,EAAE,EAAE,GAAG,GAAIL,MAAM,CAACzC,KAAK,CAACE,OAAO,GAAG,CAAE;UACpC6C,KAAK,EAAE,GAAG,GAAIN,MAAM,CAACzC,KAAK,CAACE,OAAO,GAAG,CAAE;UACvC8C,EAAE,EAAE,EAAE;UACNC,KAAK,EAAE,EAAE;UACThD,MAAM,EAAEwC,MAAM,CAACzC,KAAK,CAACC,MAAM;UAC3BC,OAAO,EAAEuC,MAAM,CAACzC,KAAK,CAACE,OAAO;UAC7BgD,KAAK,EAAE,EAAE;UACTC,SAAS,EAAE,CAAC;UACZC,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE,EAAE;UACbtD,MAAM,EAAE0C,MAAM,CAAC1C;QACjB,CAAC;QAEDO,QAAQ,CAAC;UAAEM,IAAI,EAAE,eAAe;UAAEC,OAAO,EAAE6B;QAAa,CAAC,CAAC;MAC5D,CAAC,CAAC;;MAEF;MACAH,UAAU,CAAC,MAAM;QACf/B,eAAe,CAAC8C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,CAAC,KAAK;UAAE,GAAGA,CAAC;UAAEtE,KAAK,EAAE;QAAM,CAAC,CAAC,CAAC,CAAC;MAClE,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMuE,eAAe,GAAGpD,KAAK,CAACwB,IAAI,CAACD,QAAQ,IAAI,GAAG;EAClD,MAAM8B,YAAY,GAAGrD,KAAK,CAACwB,IAAI,CAACD,QAAQ,IAAI,GAAG;EAE/C,oBACEjF,OAAA,CAACG,cAAc;IAAA6G,QAAA,gBACbhH,OAAA,CAACM,MAAM;MAAA0G,QAAA,gBACLhH,OAAA,CAACQ,KAAK;QAAAwG,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnBpH,OAAA,CAACW,UAAU;QAAC0G,OAAO,EAAErD,UAAW;QAAAgD,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAETpH,OAAA,CAACc,YAAY;MAAAkG,QAAA,gBACXhH,OAAA,CAACgB,QAAQ;QAAAgG,QAAA,gBACPhH,OAAA,CAACkB,UAAU;UAAA8F,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC7BpH,OAAA,CAACqB,gBAAgB;UAAA2F,QAAA,EAAC;QAElB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,eAEnBpH,OAAA,CAACwB,eAAe;UAAAwF,QAAA,gBACdhH,OAAA,CAAC0B,eAAe;YAACE,MAAM,EAAC,WAAW;YAAAoF,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAC9DpH,OAAA,CAAC0B,eAAe;YAACE,MAAM,EAAC,MAAM;YAAAoF,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eACzDpH,OAAA,CAAC0B,eAAe;YAACE,MAAM,EAAC,MAAM;YAAAoF,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAC1DpH,OAAA,CAAC0B,eAAe;YAACE,MAAM,EAAC,QAAQ;YAAAoF,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAElBpH,OAAA,CAAC8B,WAAW;UACVE,OAAO,EAAC,QAAQ;UAChBD,SAAS,EAAE+E,eAAgB;UAC3BO,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAAC,CAAC,EAAE;YAAEG,QAAQ,EAAE;UAAI,CAAC,CAAE;UAClDqC,QAAQ,EAAE,CAACR,eAAe,IAAIhD,WAAY;UAAAkD,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEdpH,OAAA,CAAC8B,WAAW;UACVE,OAAO,EAAC,KAAK;UACbD,SAAS,EAAEgF,YAAa;UACxBM,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAAC,EAAE,EAAE;YAAEG,QAAQ,EAAE;UAAI,CAAC,CAAE;UACnDqC,QAAQ,EAAE,CAACP,YAAY,IAAIjD,WAAY;UAAAkD,QAAA,EACxC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEXpH,OAAA,CAACgB,QAAQ;QAAAgG,QAAA,gBACPhH,OAAA,CAACkB,UAAU;UAAA8F,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC7BpH,OAAA,CAACqB,gBAAgB;UAAA2F,QAAA,EAAC;QAElB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,eAEnBpH,OAAA,CAACwB,eAAe;UAAAwF,QAAA,gBACdhH,OAAA,CAAC0B,eAAe;YAACE,MAAM,EAAC,MAAM;YAAAoF,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eACzDpH,OAAA,CAAC0B,eAAe;YAACE,MAAM,EAAC,QAAQ;YAAAoF,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAElBpH,OAAA,CAAC8B,WAAW;UACVE,OAAO,EAAC,QAAQ;UAChBD,SAAS,EAAE2B,KAAK,CAACwB,IAAI,CAACE,KAAK,IAAI,IAAK;UACpCiC,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAAC,CAAC,EAAE;YAAEM,KAAK,EAAE;UAAK,CAAC,CAAE;UAChDkC,QAAQ,EAAE5D,KAAK,CAACwB,IAAI,CAACE,KAAK,GAAG,IAAI,IAAItB,WAAY;UAAAkD,QAAA,EAClD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEdxD,YAAY,CAACe,MAAM,GAAG,CAAC,iBACtB3E,OAAA,CAACkC,gBAAgB;MAAA8E,QAAA,gBACfhH,OAAA;QAAIuH,KAAK,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,YAAY,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpH,OAAA,CAACoC,WAAW;QAAA4E,QAAA,EACTpD,YAAY,CAACgD,GAAG,CAAC,CAACd,MAAM,EAAE6B,KAAK,kBAC9B3H,OAAA,CAACsC,UAAU;UAAaV,MAAM,EAAEkE,MAAM,CAAClE,MAAO;UAACW,KAAK,EAAEuD,MAAM,CAACvD,KAAM;UAAAyE,QAAA,GAChElB,MAAM,CAACvD,KAAK,iBAAIvC,OAAA,CAAC+C,QAAQ;YAAAiE,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACvCpH,OAAA,CAACyC,eAAe;YAAAuE,QAAA,EAAElB,MAAM,CAAC1C;UAAM;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB,CAAC,eAClDpH,OAAA,CAAC2C,aAAa;YAAAqE,QAAA,EAAElB,MAAM,CAAC3C;UAAI;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC,eAC5CpH,OAAA,CAAC6C,eAAe;YAACjB,MAAM,EAAEkE,MAAM,CAAClE,MAAO;YAAAoF,QAAA,EACpClB,MAAM,CAAClE,MAAM,KAAK,WAAW,GAAG,IAAI,GACpCkE,MAAM,CAAClE,MAAM,KAAK,MAAM,GAAG,IAAI,GAC/BkE,MAAM,CAAClE,MAAM,KAAK,MAAM,GAAG,IAAI,GAAG;UAAI;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA,GARHO,KAAK;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACnB,EAEAtD,WAAW,iBACV9D,OAAA;MAAKuH,KAAK,EAAE;QACVG,SAAS,EAAE,QAAQ;QACnBF,KAAK,EAAE,SAAS;QAChBI,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE;MACV,CAAE;MAAAb,QAAA,EAAC;IAEH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAAC3D,EAAA,CAnMID,UAAoB;EAAA,QACI1D,OAAO;AAAA;AAAAgI,IAAA,GAD/BtE,UAAoB;AAqM1B,eAAeA,UAAU;AAAC,IAAAnD,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA8E,IAAA;AAAAC,YAAA,CAAA1H,EAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}