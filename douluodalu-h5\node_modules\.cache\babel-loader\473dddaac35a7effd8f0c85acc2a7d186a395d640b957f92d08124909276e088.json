{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\components\\\\BattleScene.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BattleContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n`;\n_c = BattleContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n`;\n_c2 = Header;\nconst Title = styled.h2`\n  color: #ffd700;\n  margin: 0;\n`;\n_c3 = Title;\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #ffd700;\n  color: #ffd700;\n  padding: 10px 20px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n  }\n`;\n_c4 = BackButton;\nconst BattleArea = styled.div`\n  flex: 1;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n  margin-bottom: 20px;\n`;\n_c5 = BattleArea;\nconst CharacterSide = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n`;\n_c6 = CharacterSide;\nconst CharacterCard = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 15px;\n  padding: 20px;\n  border: 2px solid #ffd700;\n  width: 100%;\n  max-width: 300px;\n`;\n_c7 = CharacterCard;\nconst CharacterAvatar = styled.div`\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 50px;\n  border: 3px solid #fff;\n  margin: 0 auto 15px;\n`;\n_c8 = CharacterAvatar;\nconst CharacterName = styled.h3`\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 15px;\n`;\n_c9 = CharacterName;\nconst HealthBar = styled.div`\n  width: 100%;\n  height: 20px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 10px;\n  overflow: hidden;\n  border: 2px solid #e74c3c;\n  margin-bottom: 10px;\n`;\n_c0 = HealthBar;\nconst HealthFill = styled.div`\n  width: ${props => props.percentage}%;\n  height: 100%;\n  background: linear-gradient(90deg, #e74c3c, #c0392b);\n  transition: width 0.5s ease;\n`;\n_c1 = HealthFill;\nconst HealthText = styled.div`\n  text-align: center;\n  color: #fff;\n  font-size: 14px;\n  margin-bottom: 15px;\n`;\n_c10 = HealthText;\nconst Stats = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n  font-size: 14px;\n`;\n_c11 = Stats;\nconst StatItem = styled.div`\n  color: #fff;\n  text-align: center;\n`;\n_c12 = StatItem;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 15px;\n  justify-content: center;\n  margin-bottom: 20px;\n`;\n_c13 = ActionButtons;\nconst ActionButton = styled.button`\n  padding: 15px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n  switch (props.variant) {\n    case 'attack':\n      return `\n          background: linear-gradient(45deg, #e74c3c, #c0392b);\n          color: white;\n          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4); }\n        `;\n    case 'skill':\n      return `\n          background: linear-gradient(45deg, #9b59b6, #8e44ad);\n          color: white;\n          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4); }\n        `;\n    case 'defend':\n      return `\n          background: linear-gradient(45deg, #3498db, #2980b9);\n          color: white;\n          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4); }\n        `;\n    default:\n      return `\n          background: linear-gradient(45deg, #ffd700, #ffed4e);\n          color: black;\n          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4); }\n        `;\n  }\n}}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c14 = ActionButton;\nconst BattleLog = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 10px;\n  padding: 15px;\n  height: 150px;\n  overflow-y: auto;\n  border: 2px solid #ffd700;\n`;\n_c15 = BattleLog;\nconst LogEntry = styled.div`\n  color: #fff;\n  margin-bottom: 5px;\n  font-size: 14px;\n  \n  &.damage {\n    color: #e74c3c;\n  }\n  \n  &.heal {\n    color: #2ecc71;\n  }\n  \n  &.info {\n    color: #3498db;\n  }\n`;\n_c16 = LogEntry;\nconst EnemySelection = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 15px;\n  margin-bottom: 20px;\n`;\n_c17 = EnemySelection;\nconst EnemyCard = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid #666;\n  border-radius: 10px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: center;\n  \n  &:hover {\n    border-color: #ffd700;\n    transform: translateY(-3px);\n  }\n`;\n_c18 = EnemyCard;\nconst enemies = [{\n  id: 1,\n  name: '森林狼',\n  level: 1,\n  hp: 80,\n  maxHp: 80,\n  attack: 15,\n  defense: 8,\n  avatar: '🐺'\n}, {\n  id: 2,\n  name: '岩石熊',\n  level: 2,\n  hp: 120,\n  maxHp: 120,\n  attack: 20,\n  defense: 15,\n  avatar: '🐻'\n}, {\n  id: 3,\n  name: '火焰鸟',\n  level: 3,\n  hp: 100,\n  maxHp: 100,\n  attack: 25,\n  defense: 10,\n  avatar: '🔥🦅'\n}, {\n  id: 4,\n  name: '冰霜虎',\n  level: 4,\n  hp: 150,\n  maxHp: 150,\n  attack: 30,\n  defense: 20,\n  avatar: '🐅'\n}];\nconst BattleScene = () => {\n  _s();\n  const {\n    state,\n    dispatch\n  } = useGame();\n  const {\n    currentCharacter,\n    battleState\n  } = state;\n  const [selectedEnemy, setSelectedEnemy] = useState(null);\n  const [isPlayerTurn, setIsPlayerTurn] = useState(true);\n  const [battleInProgress, setBattleInProgress] = useState(false);\n  const handleBack = () => {\n    dispatch({\n      type: 'SET_SCENE',\n      payload: 'main'\n    });\n    if (battleState.isInBattle) {\n      dispatch({\n        type: 'END_BATTLE'\n      });\n    }\n  };\n  const startBattle = enemy => {\n    setSelectedEnemy({\n      ...enemy\n    });\n    setBattleInProgress(true);\n    setIsPlayerTurn(true);\n    dispatch({\n      type: 'START_BATTLE',\n      payload: enemy\n    });\n    dispatch({\n      type: 'ADD_BATTLE_LOG',\n      payload: `战斗开始！你遇到了 ${enemy.name}`\n    });\n  };\n  const calculateDamage = (attacker, defender, isSkill = false) => {\n    const baseDamage = attacker.attack;\n    const defense = defender.defense;\n    const skillMultiplier = isSkill ? 1.5 : 1;\n    const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2倍随机\n\n    const damage = Math.max(1, Math.floor((baseDamage * skillMultiplier - defense * 0.5) * randomFactor));\n    return damage;\n  };\n  const playerAttack = (isSkill = false) => {\n    if (!currentCharacter || !selectedEnemy || !isPlayerTurn) return;\n    const damage = calculateDamage(currentCharacter, selectedEnemy, isSkill);\n    const newEnemyHp = Math.max(0, selectedEnemy.hp - damage);\n    setSelectedEnemy(prev => ({\n      ...prev,\n      hp: newEnemyHp\n    }));\n    const actionText = isSkill ? '使用技能攻击' : '普通攻击';\n    dispatch({\n      type: 'ADD_BATTLE_LOG',\n      payload: `${currentCharacter.name} ${actionText} ${selectedEnemy.name}，造成 ${damage} 点伤害`\n    });\n    if (newEnemyHp <= 0) {\n      // 敌人死亡\n      dispatch({\n        type: 'ADD_BATTLE_LOG',\n        payload: `${selectedEnemy.name} 被击败了！`\n      });\n\n      // 获得经验和奖励\n      const expGain = selectedEnemy.level * 20;\n      const coinGain = selectedEnemy.level * 10;\n      const updatedCharacter = {\n        ...currentCharacter,\n        exp: currentCharacter.exp + expGain\n      };\n\n      // 检查升级\n      if (updatedCharacter.exp >= updatedCharacter.maxExp) {\n        updatedCharacter.level += 1;\n        updatedCharacter.exp = updatedCharacter.exp - updatedCharacter.maxExp;\n        updatedCharacter.maxExp = Math.floor(updatedCharacter.maxExp * 1.2);\n        updatedCharacter.maxHp += 20;\n        updatedCharacter.hp = updatedCharacter.maxHp;\n        updatedCharacter.attack += 5;\n        updatedCharacter.defense += 3;\n        dispatch({\n          type: 'ADD_BATTLE_LOG',\n          payload: `${currentCharacter.name} 升级了！等级提升到 ${updatedCharacter.level}`\n        });\n      }\n      dispatch({\n        type: 'UPDATE_CHARACTER',\n        payload: updatedCharacter\n      });\n      dispatch({\n        type: 'UPDATE_CURRENCY',\n        payload: {\n          coins: coinGain\n        }\n      });\n      dispatch({\n        type: 'ADD_BATTLE_LOG',\n        payload: `获得 ${expGain} 经验值和 ${coinGain} 金币`\n      });\n\n      // 结束战斗\n      setTimeout(() => {\n        setBattleInProgress(false);\n        setSelectedEnemy(null);\n        dispatch({\n          type: 'END_BATTLE'\n        });\n      }, 2000);\n      return;\n    }\n    setIsPlayerTurn(false);\n\n    // 敌人回合\n    setTimeout(() => {\n      enemyAttack();\n    }, 1000);\n  };\n  const enemyAttack = () => {\n    if (!currentCharacter || !selectedEnemy) return;\n    const damage = calculateDamage(selectedEnemy, currentCharacter);\n    const newPlayerHp = Math.max(0, currentCharacter.hp - damage);\n    const updatedCharacter = {\n      ...currentCharacter,\n      hp: newPlayerHp\n    };\n    dispatch({\n      type: 'UPDATE_CHARACTER',\n      payload: updatedCharacter\n    });\n    dispatch({\n      type: 'ADD_BATTLE_LOG',\n      payload: `${selectedEnemy.name} 攻击 ${currentCharacter.name}，造成 ${damage} 点伤害`\n    });\n    if (newPlayerHp <= 0) {\n      dispatch({\n        type: 'ADD_BATTLE_LOG',\n        payload: `${currentCharacter.name} 被击败了...`\n      });\n      setTimeout(() => {\n        setBattleInProgress(false);\n        setSelectedEnemy(null);\n        dispatch({\n          type: 'END_BATTLE'\n        });\n        // 复活角色\n        const revivedCharacter = {\n          ...updatedCharacter,\n          hp: Math.floor(updatedCharacter.maxHp * 0.5)\n        };\n        dispatch({\n          type: 'UPDATE_CHARACTER',\n          payload: revivedCharacter\n        });\n      }, 2000);\n      return;\n    }\n    setIsPlayerTurn(true);\n  };\n  const playerDefend = () => {\n    if (!currentCharacter || !isPlayerTurn) return;\n    dispatch({\n      type: 'ADD_BATTLE_LOG',\n      payload: `${currentCharacter.name} 进入防御状态`\n    });\n    setIsPlayerTurn(false);\n    setTimeout(() => {\n      enemyAttack();\n    }, 1000);\n  };\n  if (!currentCharacter) {\n    return /*#__PURE__*/_jsxDEV(BattleContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: \"\\u6218\\u6597\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n          onClick: handleBack,\n          children: \"\\u8FD4\\u56DE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#fff',\n          textAlign: 'center'\n        },\n        children: \"\\u8BF7\\u5148\\u9009\\u62E9\\u89D2\\u8272\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(BattleContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\u6218\\u6597\\u573A\\u666F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: handleBack,\n        children: \"\\u8FD4\\u56DE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this), !battleInProgress ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#ffd700',\n          marginBottom: '20px'\n        },\n        children: \"\\u9009\\u62E9\\u5BF9\\u624B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(EnemySelection, {\n        children: enemies.map(enemy => /*#__PURE__*/_jsxDEV(EnemyCard, {\n          onClick: () => startBattle(enemy),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '40px',\n              marginBottom: '10px'\n            },\n            children: enemy.avatar\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#ffd700',\n              fontWeight: 'bold',\n              marginBottom: '5px'\n            },\n            children: enemy.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#fff',\n              fontSize: '14px'\n            },\n            children: [\"\\u7B49\\u7EA7 \", enemy.level, \" | \\u751F\\u547D\\u503C \", enemy.hp]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#fff',\n              fontSize: '12px'\n            },\n            children: [\"\\u653B\\u51FB \", enemy.attack, \" | \\u9632\\u5FA1 \", enemy.defense]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 17\n          }, this)]\n        }, enemy.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(BattleArea, {\n        children: [/*#__PURE__*/_jsxDEV(CharacterSide, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#ffd700',\n              marginBottom: '15px'\n            },\n            children: \"\\u6211\\u65B9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CharacterCard, {\n            children: [/*#__PURE__*/_jsxDEV(CharacterAvatar, {\n              children: currentCharacter.avatar\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CharacterName, {\n              children: currentCharacter.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(HealthBar, {\n              children: /*#__PURE__*/_jsxDEV(HealthFill, {\n                percentage: currentCharacter.hp / currentCharacter.maxHp * 100\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(HealthText, {\n              children: [currentCharacter.hp, \"/\", currentCharacter.maxHp]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stats, {\n              children: [/*#__PURE__*/_jsxDEV(StatItem, {\n                children: [\"\\u653B\\u51FB: \", currentCharacter.attack]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n                children: [\"\\u9632\\u5FA1: \", currentCharacter.defense]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n                children: [\"\\u901F\\u5EA6: \", currentCharacter.speed]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n                children: [\"\\u7B49\\u7EA7: \", currentCharacter.level]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CharacterSide, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#e74c3c',\n              marginBottom: '15px'\n            },\n            children: \"\\u654C\\u65B9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CharacterCard, {\n            style: {\n              borderColor: '#e74c3c'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CharacterAvatar, {\n              style: {\n                background: 'linear-gradient(45deg, #e74c3c, #c0392b)'\n              },\n              children: selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.avatar\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CharacterName, {\n              style: {\n                color: '#e74c3c'\n              },\n              children: selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(HealthBar, {\n              children: /*#__PURE__*/_jsxDEV(HealthFill, {\n                percentage: (selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.hp) / (selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.maxHp) * 100\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(HealthText, {\n              children: [selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.hp, \"/\", selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.maxHp]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stats, {\n              children: [/*#__PURE__*/_jsxDEV(StatItem, {\n                children: [\"\\u653B\\u51FB: \", selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.attack]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n                children: [\"\\u9632\\u5FA1: \", selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.defense]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n                children: [\"\\u7B49\\u7EA7: \", selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.level]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n                children: [\"\\u72B6\\u6001: \", (selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.hp) > 0 ? '存活' : '死亡']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ActionButtons, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          variant: \"attack\",\n          onClick: () => playerAttack(false),\n          disabled: !isPlayerTurn || (selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.hp) <= 0,\n          children: \"\\u666E\\u901A\\u653B\\u51FB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          variant: \"skill\",\n          onClick: () => playerAttack(true),\n          disabled: !isPlayerTurn || (selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.hp) <= 0 || currentCharacter.mp < 10,\n          children: \"\\u6280\\u80FD\\u653B\\u51FB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          variant: \"defend\",\n          onClick: playerDefend,\n          disabled: !isPlayerTurn || (selectedEnemy === null || selectedEnemy === void 0 ? void 0 : selectedEnemy.hp) <= 0,\n          children: \"\\u9632\\u5FA1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(BattleLog, {\n      children: battleState.battleLog.map((log, index) => /*#__PURE__*/_jsxDEV(LogEntry, {\n        className: log.includes('伤害') ? 'damage' : log.includes('获得') ? 'heal' : 'info',\n        children: log\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 365,\n    columnNumber: 5\n  }, this);\n};\n_s(BattleScene, \"o66fxLKOcrIliYD3ka7/VQGYnB4=\", false, function () {\n  return [useGame];\n});\n_c19 = BattleScene;\nexport default BattleScene;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19;\n$RefreshReg$(_c, \"BattleContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"BackButton\");\n$RefreshReg$(_c5, \"BattleArea\");\n$RefreshReg$(_c6, \"CharacterSide\");\n$RefreshReg$(_c7, \"CharacterCard\");\n$RefreshReg$(_c8, \"CharacterAvatar\");\n$RefreshReg$(_c9, \"CharacterName\");\n$RefreshReg$(_c0, \"HealthBar\");\n$RefreshReg$(_c1, \"HealthFill\");\n$RefreshReg$(_c10, \"HealthText\");\n$RefreshReg$(_c11, \"Stats\");\n$RefreshReg$(_c12, \"StatItem\");\n$RefreshReg$(_c13, \"ActionButtons\");\n$RefreshReg$(_c14, \"ActionButton\");\n$RefreshReg$(_c15, \"BattleLog\");\n$RefreshReg$(_c16, \"LogEntry\");\n$RefreshReg$(_c17, \"EnemySelection\");\n$RefreshReg$(_c18, \"EnemyCard\");\n$RefreshReg$(_c19, \"BattleScene\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useGame", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BattleContainer", "div", "_c", "Header", "_c2", "Title", "h2", "_c3", "BackButton", "button", "_c4", "BattleArea", "_c5", "CharacterSide", "_c6", "CharacterCard", "_c7", "<PERSON><PERSON><PERSON><PERSON>", "_c8", "CharacterName", "h3", "_c9", "HealthBar", "_c0", "HealthFill", "props", "percentage", "_c1", "HealthText", "_c10", "Stats", "_c11", "StatItem", "_c12", "ActionButtons", "_c13", "ActionButton", "variant", "_c14", "BattleLog", "_c15", "LogEntry", "_c16", "EnemySelection", "_c17", "EnemyCard", "_c18", "enemies", "id", "name", "level", "hp", "maxHp", "attack", "defense", "avatar", "BattleScene", "_s", "state", "dispatch", "currentCharacter", "battleState", "selectedEnemy", "setSelectedEnemy", "isPlayerTurn", "setIsPlayerTurn", "battleInProgress", "setBattleInProgress", "handleBack", "type", "payload", "isInBattle", "startBattle", "enemy", "calculateDamage", "attacker", "defender", "isSkill", "baseDamage", "skillMultiplier", "randomFactor", "Math", "random", "damage", "max", "floor", "<PERSON><PERSON><PERSON><PERSON>", "newEnemyHp", "prev", "actionText", "expGain", "coinGain", "updatedCharacter", "exp", "maxExp", "coins", "setTimeout", "enemyAttack", "newPlayerHp", "revivedCharacter", "playerDefend", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "color", "textAlign", "marginBottom", "map", "fontSize", "fontWeight", "speed", "borderColor", "background", "disabled", "mp", "battleLog", "log", "index", "className", "includes", "_c19", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/components/BattleScene.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\n\nconst BattleContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n`;\n\nconst Title = styled.h2`\n  color: #ffd700;\n  margin: 0;\n`;\n\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #ffd700;\n  color: #ffd700;\n  padding: 10px 20px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n  }\n`;\n\nconst BattleArea = styled.div`\n  flex: 1;\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 30px;\n  margin-bottom: 20px;\n`;\n\nconst CharacterSide = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n`;\n\nconst CharacterCard = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 15px;\n  padding: 20px;\n  border: 2px solid #ffd700;\n  width: 100%;\n  max-width: 300px;\n`;\n\nconst CharacterAvatar = styled.div`\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 50px;\n  border: 3px solid #fff;\n  margin: 0 auto 15px;\n`;\n\nconst CharacterName = styled.h3`\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 15px;\n`;\n\nconst HealthBar = styled.div`\n  width: 100%;\n  height: 20px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 10px;\n  overflow: hidden;\n  border: 2px solid #e74c3c;\n  margin-bottom: 10px;\n`;\n\nconst HealthFill = styled.div<{ percentage: number }>`\n  width: ${props => props.percentage}%;\n  height: 100%;\n  background: linear-gradient(90deg, #e74c3c, #c0392b);\n  transition: width 0.5s ease;\n`;\n\nconst HealthText = styled.div`\n  text-align: center;\n  color: #fff;\n  font-size: 14px;\n  margin-bottom: 15px;\n`;\n\nconst Stats = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n  font-size: 14px;\n`;\n\nconst StatItem = styled.div`\n  color: #fff;\n  text-align: center;\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 15px;\n  justify-content: center;\n  margin-bottom: 20px;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'attack' | 'skill' | 'defend' }>`\n  padding: 15px 25px;\n  border: none;\n  border-radius: 10px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n    switch (props.variant) {\n      case 'attack':\n        return `\n          background: linear-gradient(45deg, #e74c3c, #c0392b);\n          color: white;\n          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4); }\n        `;\n      case 'skill':\n        return `\n          background: linear-gradient(45deg, #9b59b6, #8e44ad);\n          color: white;\n          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4); }\n        `;\n      case 'defend':\n        return `\n          background: linear-gradient(45deg, #3498db, #2980b9);\n          color: white;\n          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4); }\n        `;\n      default:\n        return `\n          background: linear-gradient(45deg, #ffd700, #ffed4e);\n          color: black;\n          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4); }\n        `;\n    }\n  }}\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst BattleLog = styled.div`\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 10px;\n  padding: 15px;\n  height: 150px;\n  overflow-y: auto;\n  border: 2px solid #ffd700;\n`;\n\nconst LogEntry = styled.div`\n  color: #fff;\n  margin-bottom: 5px;\n  font-size: 14px;\n  \n  &.damage {\n    color: #e74c3c;\n  }\n  \n  &.heal {\n    color: #2ecc71;\n  }\n  \n  &.info {\n    color: #3498db;\n  }\n`;\n\nconst EnemySelection = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 15px;\n  margin-bottom: 20px;\n`;\n\nconst EnemyCard = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid #666;\n  border-radius: 10px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: center;\n  \n  &:hover {\n    border-color: #ffd700;\n    transform: translateY(-3px);\n  }\n`;\n\nconst enemies = [\n  { id: 1, name: '森林狼', level: 1, hp: 80, maxHp: 80, attack: 15, defense: 8, avatar: '🐺' },\n  { id: 2, name: '岩石熊', level: 2, hp: 120, maxHp: 120, attack: 20, defense: 15, avatar: '🐻' },\n  { id: 3, name: '火焰鸟', level: 3, hp: 100, maxHp: 100, attack: 25, defense: 10, avatar: '🔥🦅' },\n  { id: 4, name: '冰霜虎', level: 4, hp: 150, maxHp: 150, attack: 30, defense: 20, avatar: '🐅' },\n];\n\nconst BattleScene: React.FC = () => {\n  const { state, dispatch } = useGame();\n  const { currentCharacter, battleState } = state;\n  const [selectedEnemy, setSelectedEnemy] = useState<any>(null);\n  const [isPlayerTurn, setIsPlayerTurn] = useState(true);\n  const [battleInProgress, setBattleInProgress] = useState(false);\n\n  const handleBack = () => {\n    dispatch({ type: 'SET_SCENE', payload: 'main' });\n    if (battleState.isInBattle) {\n      dispatch({ type: 'END_BATTLE' });\n    }\n  };\n\n  const startBattle = (enemy: any) => {\n    setSelectedEnemy({ ...enemy });\n    setBattleInProgress(true);\n    setIsPlayerTurn(true);\n    dispatch({ type: 'START_BATTLE', payload: enemy });\n    dispatch({ type: 'ADD_BATTLE_LOG', payload: `战斗开始！你遇到了 ${enemy.name}` });\n  };\n\n  const calculateDamage = (attacker: any, defender: any, isSkill = false) => {\n    const baseDamage = attacker.attack;\n    const defense = defender.defense;\n    const skillMultiplier = isSkill ? 1.5 : 1;\n    const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2倍随机\n    \n    const damage = Math.max(1, Math.floor((baseDamage * skillMultiplier - defense * 0.5) * randomFactor));\n    return damage;\n  };\n\n  const playerAttack = (isSkill = false) => {\n    if (!currentCharacter || !selectedEnemy || !isPlayerTurn) return;\n\n    const damage = calculateDamage(currentCharacter, selectedEnemy, isSkill);\n    const newEnemyHp = Math.max(0, selectedEnemy.hp - damage);\n    \n    setSelectedEnemy((prev: any) => ({ ...prev, hp: newEnemyHp }));\n    \n    const actionText = isSkill ? '使用技能攻击' : '普通攻击';\n    dispatch({ type: 'ADD_BATTLE_LOG', payload: `${currentCharacter.name} ${actionText} ${selectedEnemy.name}，造成 ${damage} 点伤害` });\n\n    if (newEnemyHp <= 0) {\n      // 敌人死亡\n      dispatch({ type: 'ADD_BATTLE_LOG', payload: `${selectedEnemy.name} 被击败了！` });\n      \n      // 获得经验和奖励\n      const expGain = selectedEnemy.level * 20;\n      const coinGain = selectedEnemy.level * 10;\n      \n      const updatedCharacter = {\n        ...currentCharacter,\n        exp: currentCharacter.exp + expGain,\n      };\n      \n      // 检查升级\n      if (updatedCharacter.exp >= updatedCharacter.maxExp) {\n        updatedCharacter.level += 1;\n        updatedCharacter.exp = updatedCharacter.exp - updatedCharacter.maxExp;\n        updatedCharacter.maxExp = Math.floor(updatedCharacter.maxExp * 1.2);\n        updatedCharacter.maxHp += 20;\n        updatedCharacter.hp = updatedCharacter.maxHp;\n        updatedCharacter.attack += 5;\n        updatedCharacter.defense += 3;\n        \n        dispatch({ type: 'ADD_BATTLE_LOG', payload: `${currentCharacter.name} 升级了！等级提升到 ${updatedCharacter.level}` });\n      }\n      \n      dispatch({ type: 'UPDATE_CHARACTER', payload: updatedCharacter });\n      dispatch({ type: 'UPDATE_CURRENCY', payload: { coins: coinGain } });\n      dispatch({ type: 'ADD_BATTLE_LOG', payload: `获得 ${expGain} 经验值和 ${coinGain} 金币` });\n      \n      // 结束战斗\n      setTimeout(() => {\n        setBattleInProgress(false);\n        setSelectedEnemy(null);\n        dispatch({ type: 'END_BATTLE' });\n      }, 2000);\n      \n      return;\n    }\n\n    setIsPlayerTurn(false);\n    \n    // 敌人回合\n    setTimeout(() => {\n      enemyAttack();\n    }, 1000);\n  };\n\n  const enemyAttack = () => {\n    if (!currentCharacter || !selectedEnemy) return;\n\n    const damage = calculateDamage(selectedEnemy, currentCharacter);\n    const newPlayerHp = Math.max(0, currentCharacter.hp - damage);\n    \n    const updatedCharacter = { ...currentCharacter, hp: newPlayerHp };\n    dispatch({ type: 'UPDATE_CHARACTER', payload: updatedCharacter });\n    dispatch({ type: 'ADD_BATTLE_LOG', payload: `${selectedEnemy.name} 攻击 ${currentCharacter.name}，造成 ${damage} 点伤害` });\n\n    if (newPlayerHp <= 0) {\n      dispatch({ type: 'ADD_BATTLE_LOG', payload: `${currentCharacter.name} 被击败了...` });\n      setTimeout(() => {\n        setBattleInProgress(false);\n        setSelectedEnemy(null);\n        dispatch({ type: 'END_BATTLE' });\n        // 复活角色\n        const revivedCharacter = { ...updatedCharacter, hp: Math.floor(updatedCharacter.maxHp * 0.5) };\n        dispatch({ type: 'UPDATE_CHARACTER', payload: revivedCharacter });\n      }, 2000);\n      return;\n    }\n\n    setIsPlayerTurn(true);\n  };\n\n  const playerDefend = () => {\n    if (!currentCharacter || !isPlayerTurn) return;\n    \n    dispatch({ type: 'ADD_BATTLE_LOG', payload: `${currentCharacter.name} 进入防御状态` });\n    setIsPlayerTurn(false);\n    \n    setTimeout(() => {\n      enemyAttack();\n    }, 1000);\n  };\n\n  if (!currentCharacter) {\n    return (\n      <BattleContainer>\n        <Header>\n          <Title>战斗</Title>\n          <BackButton onClick={handleBack}>返回</BackButton>\n        </Header>\n        <div style={{ color: '#fff', textAlign: 'center' }}>请先选择角色</div>\n      </BattleContainer>\n    );\n  }\n\n  return (\n    <BattleContainer>\n      <Header>\n        <Title>战斗场景</Title>\n        <BackButton onClick={handleBack}>返回</BackButton>\n      </Header>\n\n      {!battleInProgress ? (\n        <>\n          <h3 style={{ color: '#ffd700', marginBottom: '20px' }}>选择对手</h3>\n          <EnemySelection>\n            {enemies.map(enemy => (\n              <EnemyCard key={enemy.id} onClick={() => startBattle(enemy)}>\n                <div style={{ fontSize: '40px', marginBottom: '10px' }}>{enemy.avatar}</div>\n                <div style={{ color: '#ffd700', fontWeight: 'bold', marginBottom: '5px' }}>\n                  {enemy.name}\n                </div>\n                <div style={{ color: '#fff', fontSize: '14px' }}>\n                  等级 {enemy.level} | 生命值 {enemy.hp}\n                </div>\n                <div style={{ color: '#fff', fontSize: '12px' }}>\n                  攻击 {enemy.attack} | 防御 {enemy.defense}\n                </div>\n              </EnemyCard>\n            ))}\n          </EnemySelection>\n        </>\n      ) : (\n        <>\n          <BattleArea>\n            <CharacterSide>\n              <h3 style={{ color: '#ffd700', marginBottom: '15px' }}>我方</h3>\n              <CharacterCard>\n                <CharacterAvatar>{currentCharacter.avatar}</CharacterAvatar>\n                <CharacterName>{currentCharacter.name}</CharacterName>\n                <HealthBar>\n                  <HealthFill percentage={(currentCharacter.hp / currentCharacter.maxHp) * 100} />\n                </HealthBar>\n                <HealthText>{currentCharacter.hp}/{currentCharacter.maxHp}</HealthText>\n                <Stats>\n                  <StatItem>攻击: {currentCharacter.attack}</StatItem>\n                  <StatItem>防御: {currentCharacter.defense}</StatItem>\n                  <StatItem>速度: {currentCharacter.speed}</StatItem>\n                  <StatItem>等级: {currentCharacter.level}</StatItem>\n                </Stats>\n              </CharacterCard>\n            </CharacterSide>\n\n            <CharacterSide>\n              <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>敌方</h3>\n              <CharacterCard style={{ borderColor: '#e74c3c' }}>\n                <CharacterAvatar style={{ background: 'linear-gradient(45deg, #e74c3c, #c0392b)' }}>\n                  {selectedEnemy?.avatar}\n                </CharacterAvatar>\n                <CharacterName style={{ color: '#e74c3c' }}>{selectedEnemy?.name}</CharacterName>\n                <HealthBar>\n                  <HealthFill percentage={(selectedEnemy?.hp / selectedEnemy?.maxHp) * 100} />\n                </HealthBar>\n                <HealthText>{selectedEnemy?.hp}/{selectedEnemy?.maxHp}</HealthText>\n                <Stats>\n                  <StatItem>攻击: {selectedEnemy?.attack}</StatItem>\n                  <StatItem>防御: {selectedEnemy?.defense}</StatItem>\n                  <StatItem>等级: {selectedEnemy?.level}</StatItem>\n                  <StatItem>状态: {selectedEnemy?.hp > 0 ? '存活' : '死亡'}</StatItem>\n                </Stats>\n              </CharacterCard>\n            </CharacterSide>\n          </BattleArea>\n\n          <ActionButtons>\n            <ActionButton \n              variant=\"attack\" \n              onClick={() => playerAttack(false)}\n              disabled={!isPlayerTurn || selectedEnemy?.hp <= 0}\n            >\n              普通攻击\n            </ActionButton>\n            <ActionButton \n              variant=\"skill\" \n              onClick={() => playerAttack(true)}\n              disabled={!isPlayerTurn || selectedEnemy?.hp <= 0 || currentCharacter.mp < 10}\n            >\n              技能攻击\n            </ActionButton>\n            <ActionButton \n              variant=\"defend\" \n              onClick={playerDefend}\n              disabled={!isPlayerTurn || selectedEnemy?.hp <= 0}\n            >\n              防御\n            </ActionButton>\n          </ActionButtons>\n        </>\n      )}\n\n      <BattleLog>\n        {battleState.battleLog.map((log, index) => (\n          <LogEntry \n            key={index} \n            className={log.includes('伤害') ? 'damage' : log.includes('获得') ? 'heal' : 'info'}\n          >\n            {log}\n          </LogEntry>\n        ))}\n      </BattleLog>\n    </BattleContainer>\n  );\n};\n\nexport default BattleScene;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,eAAe,GAAGN,MAAM,CAACO,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,eAAe;AAOrB,MAAMG,MAAM,GAAGT,MAAM,CAACO,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,MAAM;AAOZ,MAAME,KAAK,GAAGX,MAAM,CAACY,EAAE;AACvB;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,UAAU,GAAGd,MAAM,CAACe,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,UAAU;AAehB,MAAMG,UAAU,GAAGjB,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GANID,UAAU;AAQhB,MAAME,aAAa,GAAGnB,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAJID,aAAa;AAMnB,MAAME,aAAa,GAAGrB,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAPID,aAAa;AASnB,MAAME,eAAe,GAAGvB,MAAM,CAACO,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAXID,eAAe;AAarB,MAAME,aAAa,GAAGzB,MAAM,CAAC0B,EAAE;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,aAAa;AAMnB,MAAMG,SAAS,GAAG5B,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GARID,SAAS;AAUf,MAAME,UAAU,GAAG9B,MAAM,CAACO,GAA2B;AACrD,WAAWwB,KAAK,IAAIA,KAAK,CAACC,UAAU;AACpC;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIH,UAAU;AAOhB,MAAMI,UAAU,GAAGlC,MAAM,CAACO,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GALID,UAAU;AAOhB,MAAME,KAAK,GAAGpC,MAAM,CAACO,GAAG;AACxB;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GALID,KAAK;AAOX,MAAME,QAAQ,GAAGtC,MAAM,CAACO,GAAG;AAC3B;AACA;AACA,CAAC;AAACgC,IAAA,GAHID,QAAQ;AAKd,MAAME,aAAa,GAAGxC,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GALID,aAAa;AAOnB,MAAME,YAAY,GAAG1C,MAAM,CAACe,MAAmD;AAC/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgB,KAAK,IAAI;EACT,QAAQA,KAAK,CAACY,OAAO;IACnB,KAAK,QAAQ;MACX,OAAO;AACf;AACA;AACA;AACA,SAAS;IACH,KAAK,OAAO;MACV,OAAO;AACf;AACA;AACA;AACA,SAAS;IACH,KAAK,QAAQ;MACX,OAAO;AACf;AACA;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA1CIF,YAAY;AA4ClB,MAAMG,SAAS,GAAG7C,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuC,IAAA,GAPID,SAAS;AASf,MAAME,QAAQ,GAAG/C,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyC,IAAA,GAhBID,QAAQ;AAkBd,MAAME,cAAc,GAAGjD,MAAM,CAACO,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GALID,cAAc;AAOpB,MAAME,SAAS,GAAGnD,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GAbID,SAAS;AAef,MAAME,OAAO,GAAG,CACd;EAAEC,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,CAAC;EAAEC,EAAE,EAAE,EAAE;EAAEC,KAAK,EAAE,EAAE;EAAEC,MAAM,EAAE,EAAE;EAAEC,OAAO,EAAE,CAAC;EAAEC,MAAM,EAAE;AAAK,CAAC,EACzF;EAAEP,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,CAAC;EAAEC,EAAE,EAAE,GAAG;EAAEC,KAAK,EAAE,GAAG;EAAEC,MAAM,EAAE,EAAE;EAAEC,OAAO,EAAE,EAAE;EAAEC,MAAM,EAAE;AAAK,CAAC,EAC5F;EAAEP,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,CAAC;EAAEC,EAAE,EAAE,GAAG;EAAEC,KAAK,EAAE,GAAG;EAAEC,MAAM,EAAE,EAAE;EAAEC,OAAO,EAAE,EAAE;EAAEC,MAAM,EAAE;AAAO,CAAC,EAC9F;EAAEP,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,KAAK;EAAEC,KAAK,EAAE,CAAC;EAAEC,EAAE,EAAE,GAAG;EAAEC,KAAK,EAAE,GAAG;EAAEC,MAAM,EAAE,EAAE;EAAEC,OAAO,EAAE,EAAE;EAAEC,MAAM,EAAE;AAAK,CAAC,CAC7F;AAED,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAGhE,OAAO,CAAC,CAAC;EACrC,MAAM;IAAEiE,gBAAgB;IAAEC;EAAY,CAAC,GAAGH,KAAK;EAC/C,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAACuE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAM2E,UAAU,GAAGA,CAAA,KAAM;IACvBT,QAAQ,CAAC;MAAEU,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAO,CAAC,CAAC;IAChD,IAAIT,WAAW,CAACU,UAAU,EAAE;MAC1BZ,QAAQ,CAAC;QAAEU,IAAI,EAAE;MAAa,CAAC,CAAC;IAClC;EACF,CAAC;EAED,MAAMG,WAAW,GAAIC,KAAU,IAAK;IAClCV,gBAAgB,CAAC;MAAE,GAAGU;IAAM,CAAC,CAAC;IAC9BN,mBAAmB,CAAC,IAAI,CAAC;IACzBF,eAAe,CAAC,IAAI,CAAC;IACrBN,QAAQ,CAAC;MAAEU,IAAI,EAAE,cAAc;MAAEC,OAAO,EAAEG;IAAM,CAAC,CAAC;IAClDd,QAAQ,CAAC;MAAEU,IAAI,EAAE,gBAAgB;MAAEC,OAAO,EAAE,aAAaG,KAAK,CAACxB,IAAI;IAAG,CAAC,CAAC;EAC1E,CAAC;EAED,MAAMyB,eAAe,GAAGA,CAACC,QAAa,EAAEC,QAAa,EAAEC,OAAO,GAAG,KAAK,KAAK;IACzE,MAAMC,UAAU,GAAGH,QAAQ,CAACtB,MAAM;IAClC,MAAMC,OAAO,GAAGsB,QAAQ,CAACtB,OAAO;IAChC,MAAMyB,eAAe,GAAGF,OAAO,GAAG,GAAG,GAAG,CAAC;IACzC,MAAMG,YAAY,GAAG,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;;IAEhD,MAAMC,MAAM,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,KAAK,CAAC,CAACP,UAAU,GAAGC,eAAe,GAAGzB,OAAO,GAAG,GAAG,IAAI0B,YAAY,CAAC,CAAC;IACrG,OAAOG,MAAM;EACf,CAAC;EAED,MAAMG,YAAY,GAAGA,CAACT,OAAO,GAAG,KAAK,KAAK;IACxC,IAAI,CAACjB,gBAAgB,IAAI,CAACE,aAAa,IAAI,CAACE,YAAY,EAAE;IAE1D,MAAMmB,MAAM,GAAGT,eAAe,CAACd,gBAAgB,EAAEE,aAAa,EAAEe,OAAO,CAAC;IACxE,MAAMU,UAAU,GAAGN,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEtB,aAAa,CAACX,EAAE,GAAGgC,MAAM,CAAC;IAEzDpB,gBAAgB,CAAEyB,IAAS,KAAM;MAAE,GAAGA,IAAI;MAAErC,EAAE,EAAEoC;IAAW,CAAC,CAAC,CAAC;IAE9D,MAAME,UAAU,GAAGZ,OAAO,GAAG,QAAQ,GAAG,MAAM;IAC9ClB,QAAQ,CAAC;MAAEU,IAAI,EAAE,gBAAgB;MAAEC,OAAO,EAAE,GAAGV,gBAAgB,CAACX,IAAI,IAAIwC,UAAU,IAAI3B,aAAa,CAACb,IAAI,OAAOkC,MAAM;IAAO,CAAC,CAAC;IAE9H,IAAII,UAAU,IAAI,CAAC,EAAE;MACnB;MACA5B,QAAQ,CAAC;QAAEU,IAAI,EAAE,gBAAgB;QAAEC,OAAO,EAAE,GAAGR,aAAa,CAACb,IAAI;MAAS,CAAC,CAAC;;MAE5E;MACA,MAAMyC,OAAO,GAAG5B,aAAa,CAACZ,KAAK,GAAG,EAAE;MACxC,MAAMyC,QAAQ,GAAG7B,aAAa,CAACZ,KAAK,GAAG,EAAE;MAEzC,MAAM0C,gBAAgB,GAAG;QACvB,GAAGhC,gBAAgB;QACnBiC,GAAG,EAAEjC,gBAAgB,CAACiC,GAAG,GAAGH;MAC9B,CAAC;;MAED;MACA,IAAIE,gBAAgB,CAACC,GAAG,IAAID,gBAAgB,CAACE,MAAM,EAAE;QACnDF,gBAAgB,CAAC1C,KAAK,IAAI,CAAC;QAC3B0C,gBAAgB,CAACC,GAAG,GAAGD,gBAAgB,CAACC,GAAG,GAAGD,gBAAgB,CAACE,MAAM;QACrEF,gBAAgB,CAACE,MAAM,GAAGb,IAAI,CAACI,KAAK,CAACO,gBAAgB,CAACE,MAAM,GAAG,GAAG,CAAC;QACnEF,gBAAgB,CAACxC,KAAK,IAAI,EAAE;QAC5BwC,gBAAgB,CAACzC,EAAE,GAAGyC,gBAAgB,CAACxC,KAAK;QAC5CwC,gBAAgB,CAACvC,MAAM,IAAI,CAAC;QAC5BuC,gBAAgB,CAACtC,OAAO,IAAI,CAAC;QAE7BK,QAAQ,CAAC;UAAEU,IAAI,EAAE,gBAAgB;UAAEC,OAAO,EAAE,GAAGV,gBAAgB,CAACX,IAAI,cAAc2C,gBAAgB,CAAC1C,KAAK;QAAG,CAAC,CAAC;MAC/G;MAEAS,QAAQ,CAAC;QAAEU,IAAI,EAAE,kBAAkB;QAAEC,OAAO,EAAEsB;MAAiB,CAAC,CAAC;MACjEjC,QAAQ,CAAC;QAAEU,IAAI,EAAE,iBAAiB;QAAEC,OAAO,EAAE;UAAEyB,KAAK,EAAEJ;QAAS;MAAE,CAAC,CAAC;MACnEhC,QAAQ,CAAC;QAAEU,IAAI,EAAE,gBAAgB;QAAEC,OAAO,EAAE,MAAMoB,OAAO,SAASC,QAAQ;MAAM,CAAC,CAAC;;MAElF;MACAK,UAAU,CAAC,MAAM;QACf7B,mBAAmB,CAAC,KAAK,CAAC;QAC1BJ,gBAAgB,CAAC,IAAI,CAAC;QACtBJ,QAAQ,CAAC;UAAEU,IAAI,EAAE;QAAa,CAAC,CAAC;MAClC,CAAC,EAAE,IAAI,CAAC;MAER;IACF;IAEAJ,eAAe,CAAC,KAAK,CAAC;;IAEtB;IACA+B,UAAU,CAAC,MAAM;MACfC,WAAW,CAAC,CAAC;IACf,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMA,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACrC,gBAAgB,IAAI,CAACE,aAAa,EAAE;IAEzC,MAAMqB,MAAM,GAAGT,eAAe,CAACZ,aAAa,EAAEF,gBAAgB,CAAC;IAC/D,MAAMsC,WAAW,GAAGjB,IAAI,CAACG,GAAG,CAAC,CAAC,EAAExB,gBAAgB,CAACT,EAAE,GAAGgC,MAAM,CAAC;IAE7D,MAAMS,gBAAgB,GAAG;MAAE,GAAGhC,gBAAgB;MAAET,EAAE,EAAE+C;IAAY,CAAC;IACjEvC,QAAQ,CAAC;MAAEU,IAAI,EAAE,kBAAkB;MAAEC,OAAO,EAAEsB;IAAiB,CAAC,CAAC;IACjEjC,QAAQ,CAAC;MAAEU,IAAI,EAAE,gBAAgB;MAAEC,OAAO,EAAE,GAAGR,aAAa,CAACb,IAAI,OAAOW,gBAAgB,CAACX,IAAI,OAAOkC,MAAM;IAAO,CAAC,CAAC;IAEnH,IAAIe,WAAW,IAAI,CAAC,EAAE;MACpBvC,QAAQ,CAAC;QAAEU,IAAI,EAAE,gBAAgB;QAAEC,OAAO,EAAE,GAAGV,gBAAgB,CAACX,IAAI;MAAW,CAAC,CAAC;MACjF+C,UAAU,CAAC,MAAM;QACf7B,mBAAmB,CAAC,KAAK,CAAC;QAC1BJ,gBAAgB,CAAC,IAAI,CAAC;QACtBJ,QAAQ,CAAC;UAAEU,IAAI,EAAE;QAAa,CAAC,CAAC;QAChC;QACA,MAAM8B,gBAAgB,GAAG;UAAE,GAAGP,gBAAgB;UAAEzC,EAAE,EAAE8B,IAAI,CAACI,KAAK,CAACO,gBAAgB,CAACxC,KAAK,GAAG,GAAG;QAAE,CAAC;QAC9FO,QAAQ,CAAC;UAAEU,IAAI,EAAE,kBAAkB;UAAEC,OAAO,EAAE6B;QAAiB,CAAC,CAAC;MACnE,CAAC,EAAE,IAAI,CAAC;MACR;IACF;IAEAlC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACxC,gBAAgB,IAAI,CAACI,YAAY,EAAE;IAExCL,QAAQ,CAAC;MAAEU,IAAI,EAAE,gBAAgB;MAAEC,OAAO,EAAE,GAAGV,gBAAgB,CAACX,IAAI;IAAU,CAAC,CAAC;IAChFgB,eAAe,CAAC,KAAK,CAAC;IAEtB+B,UAAU,CAAC,MAAM;MACfC,WAAW,CAAC,CAAC;IACf,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,IAAI,CAACrC,gBAAgB,EAAE;IACrB,oBACE/D,OAAA,CAACG,eAAe;MAAAqG,QAAA,gBACdxG,OAAA,CAACM,MAAM;QAAAkG,QAAA,gBACLxG,OAAA,CAACQ,KAAK;UAAAgG,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjB5G,OAAA,CAACW,UAAU;UAACkG,OAAO,EAAEtC,UAAW;UAAAiC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACT5G,OAAA;QAAK8G,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAR,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAEtB;EAEA,oBACE5G,OAAA,CAACG,eAAe;IAAAqG,QAAA,gBACdxG,OAAA,CAACM,MAAM;MAAAkG,QAAA,gBACLxG,OAAA,CAACQ,KAAK;QAAAgG,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnB5G,OAAA,CAACW,UAAU;QAACkG,OAAO,EAAEtC,UAAW;QAAAiC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,EAER,CAACvC,gBAAgB,gBAChBrE,OAAA,CAAAE,SAAA;MAAAsG,QAAA,gBACExG,OAAA;QAAI8G,KAAK,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE5G,OAAA,CAAC8C,cAAc;QAAA0D,QAAA,EACZtD,OAAO,CAACgE,GAAG,CAACtC,KAAK,iBAChB5E,OAAA,CAACgD,SAAS;UAAgB6D,OAAO,EAAEA,CAAA,KAAMlC,WAAW,CAACC,KAAK,CAAE;UAAA4B,QAAA,gBAC1DxG,OAAA;YAAK8G,KAAK,EAAE;cAAEK,QAAQ,EAAE,MAAM;cAAEF,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAE5B,KAAK,CAAClB;UAAM;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5E5G,OAAA;YAAK8G,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEK,UAAU,EAAE,MAAM;cAAEH,YAAY,EAAE;YAAM,CAAE;YAAAT,QAAA,EACvE5B,KAAK,CAACxB;UAAI;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN5G,OAAA;YAAK8G,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEI,QAAQ,EAAE;YAAO,CAAE;YAAAX,QAAA,GAAC,eAC5C,EAAC5B,KAAK,CAACvB,KAAK,EAAC,wBAAO,EAACuB,KAAK,CAACtB,EAAE;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACN5G,OAAA;YAAK8G,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEI,QAAQ,EAAE;YAAO,CAAE;YAAAX,QAAA,GAAC,eAC5C,EAAC5B,KAAK,CAACpB,MAAM,EAAC,kBAAM,EAACoB,KAAK,CAACnB,OAAO;UAAA;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA,GAVQhC,KAAK,CAACzB,EAAE;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWb,CACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA,eACjB,CAAC,gBAEH5G,OAAA,CAAAE,SAAA;MAAAsG,QAAA,gBACExG,OAAA,CAACc,UAAU;QAAA0F,QAAA,gBACTxG,OAAA,CAACgB,aAAa;UAAAwF,QAAA,gBACZxG,OAAA;YAAI8G,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEE,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9D5G,OAAA,CAACkB,aAAa;YAAAsF,QAAA,gBACZxG,OAAA,CAACoB,eAAe;cAAAoF,QAAA,EAAEzC,gBAAgB,CAACL;YAAM;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkB,CAAC,eAC5D5G,OAAA,CAACsB,aAAa;cAAAkF,QAAA,EAAEzC,gBAAgB,CAACX;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC,eACtD5G,OAAA,CAACyB,SAAS;cAAA+E,QAAA,eACRxG,OAAA,CAAC2B,UAAU;gBAACE,UAAU,EAAGkC,gBAAgB,CAACT,EAAE,GAAGS,gBAAgB,CAACR,KAAK,GAAI;cAAI;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACZ5G,OAAA,CAAC+B,UAAU;cAAAyE,QAAA,GAAEzC,gBAAgB,CAACT,EAAE,EAAC,GAAC,EAACS,gBAAgB,CAACR,KAAK;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACvE5G,OAAA,CAACiC,KAAK;cAAAuE,QAAA,gBACJxG,OAAA,CAACmC,QAAQ;gBAAAqE,QAAA,GAAC,gBAAI,EAACzC,gBAAgB,CAACP,MAAM;cAAA;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClD5G,OAAA,CAACmC,QAAQ;gBAAAqE,QAAA,GAAC,gBAAI,EAACzC,gBAAgB,CAACN,OAAO;cAAA;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnD5G,OAAA,CAACmC,QAAQ;gBAAAqE,QAAA,GAAC,gBAAI,EAACzC,gBAAgB,CAACsD,KAAK;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjD5G,OAAA,CAACmC,QAAQ;gBAAAqE,QAAA,GAAC,gBAAI,EAACzC,gBAAgB,CAACV,KAAK;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEhB5G,OAAA,CAACgB,aAAa;UAAAwF,QAAA,gBACZxG,OAAA;YAAI8G,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEE,YAAY,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9D5G,OAAA,CAACkB,aAAa;YAAC4F,KAAK,EAAE;cAAEQ,WAAW,EAAE;YAAU,CAAE;YAAAd,QAAA,gBAC/CxG,OAAA,CAACoB,eAAe;cAAC0F,KAAK,EAAE;gBAAES,UAAU,EAAE;cAA2C,CAAE;cAAAf,QAAA,EAChFvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEP;YAAM;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAClB5G,OAAA,CAACsB,aAAa;cAACwF,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAP,QAAA,EAAEvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEb;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC,eACjF5G,OAAA,CAACyB,SAAS;cAAA+E,QAAA,eACRxG,OAAA,CAAC2B,UAAU;gBAACE,UAAU,EAAG,CAAAoC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEX,EAAE,KAAGW,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEV,KAAK,IAAI;cAAI;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACZ5G,OAAA,CAAC+B,UAAU;cAAAyE,QAAA,GAAEvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEX,EAAE,EAAC,GAAC,EAACW,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEV,KAAK;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACnE5G,OAAA,CAACiC,KAAK;cAAAuE,QAAA,gBACJxG,OAAA,CAACmC,QAAQ;gBAAAqE,QAAA,GAAC,gBAAI,EAACvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAET,MAAM;cAAA;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChD5G,OAAA,CAACmC,QAAQ;gBAAAqE,QAAA,GAAC,gBAAI,EAACvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAER,OAAO;cAAA;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjD5G,OAAA,CAACmC,QAAQ;gBAAAqE,QAAA,GAAC,gBAAI,EAACvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEZ,KAAK;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/C5G,OAAA,CAACmC,QAAQ;gBAAAqE,QAAA,GAAC,gBAAI,EAAC,CAAAvC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEX,EAAE,IAAG,CAAC,GAAG,IAAI,GAAG,IAAI;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEb5G,OAAA,CAACqC,aAAa;QAAAmE,QAAA,gBACZxG,OAAA,CAACuC,YAAY;UACXC,OAAO,EAAC,QAAQ;UAChBqE,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAAC,KAAK,CAAE;UACnC+B,QAAQ,EAAE,CAACrD,YAAY,IAAI,CAAAF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEX,EAAE,KAAI,CAAE;UAAAkD,QAAA,EACnD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACf5G,OAAA,CAACuC,YAAY;UACXC,OAAO,EAAC,OAAO;UACfqE,OAAO,EAAEA,CAAA,KAAMpB,YAAY,CAAC,IAAI,CAAE;UAClC+B,QAAQ,EAAE,CAACrD,YAAY,IAAI,CAAAF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEX,EAAE,KAAI,CAAC,IAAIS,gBAAgB,CAAC0D,EAAE,GAAG,EAAG;UAAAjB,QAAA,EAC/E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACf5G,OAAA,CAACuC,YAAY;UACXC,OAAO,EAAC,QAAQ;UAChBqE,OAAO,EAAEN,YAAa;UACtBiB,QAAQ,EAAE,CAACrD,YAAY,IAAI,CAAAF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEX,EAAE,KAAI,CAAE;UAAAkD,QAAA,EACnD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA,eAChB,CACH,eAED5G,OAAA,CAAC0C,SAAS;MAAA8D,QAAA,EACPxC,WAAW,CAAC0D,SAAS,CAACR,GAAG,CAAC,CAACS,GAAG,EAAEC,KAAK,kBACpC5H,OAAA,CAAC4C,QAAQ;QAEPiF,SAAS,EAAEF,GAAG,CAACG,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAGH,GAAG,CAACG,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,MAAO;QAAAtB,QAAA,EAE/EmB;MAAG,GAHCC,KAAK;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIF,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAAChD,EAAA,CAvPID,WAAqB;EAAA,QACG7D,OAAO;AAAA;AAAAiI,IAAA,GAD/BpE,WAAqB;AAyP3B,eAAeA,WAAW;AAAC,IAAAtD,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA8E,IAAA;AAAAC,YAAA,CAAA3H,EAAA;AAAA2H,YAAA,CAAAzH,GAAA;AAAAyH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAnF,IAAA;AAAAmF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}