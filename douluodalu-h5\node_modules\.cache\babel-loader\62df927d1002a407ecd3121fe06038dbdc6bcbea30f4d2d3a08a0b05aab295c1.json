{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\context\\\\GameContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer } from 'react';\n\n// 游戏状态接口\n\n// 动作类型\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 初始状态\nconst initialState = {\n  user: {\n    id: '',\n    username: '',\n    vipLevel: 0,\n    diamonds: 100,\n    coins: 1000,\n    energy: 100,\n    maxEnergy: 100\n  },\n  characters: [],\n  currentCharacter: null,\n  inventory: [],\n  currentScene: 'main',\n  battleState: {\n    isInBattle: false,\n    enemy: null,\n    battleLog: []\n  }\n};\n\n// Reducer\nfunction gameReducer(state, action) {\n  var _state$currentCharact, _action$payload$diamo, _action$payload$coins;\n  switch (action.type) {\n    case 'SET_USER':\n      return {\n        ...state,\n        user: {\n          ...state.user,\n          ...action.payload\n        }\n      };\n    case 'SET_CURRENT_CHARACTER':\n      return {\n        ...state,\n        currentCharacter: action.payload\n      };\n    case 'UPDATE_CHARACTER':\n      return {\n        ...state,\n        characters: state.characters.map(char => char.id === action.payload.id ? action.payload : char),\n        currentCharacter: ((_state$currentCharact = state.currentCharacter) === null || _state$currentCharact === void 0 ? void 0 : _state$currentCharact.id) === action.payload.id ? action.payload : state.currentCharacter\n      };\n    case 'ADD_CHARACTER':\n      return {\n        ...state,\n        characters: [...state.characters, action.payload]\n      };\n    case 'SET_SCENE':\n      return {\n        ...state,\n        currentScene: action.payload\n      };\n    case 'UPDATE_CURRENCY':\n      return {\n        ...state,\n        user: {\n          ...state.user,\n          diamonds: (_action$payload$diamo = action.payload.diamonds) !== null && _action$payload$diamo !== void 0 ? _action$payload$diamo : state.user.diamonds,\n          coins: (_action$payload$coins = action.payload.coins) !== null && _action$payload$coins !== void 0 ? _action$payload$coins : state.user.coins\n        }\n      };\n    case 'START_BATTLE':\n      return {\n        ...state,\n        battleState: {\n          isInBattle: true,\n          enemy: action.payload,\n          battleLog: []\n        }\n      };\n    case 'END_BATTLE':\n      return {\n        ...state,\n        battleState: {\n          ...state.battleState,\n          isInBattle: false,\n          enemy: null\n        }\n      };\n    case 'ADD_BATTLE_LOG':\n      return {\n        ...state,\n        battleState: {\n          ...state.battleState,\n          battleLog: [...state.battleState.battleLog, action.payload]\n        }\n      };\n    default:\n      return state;\n  }\n}\n\n// Context\nconst GameContext = /*#__PURE__*/createContext(null);\n\n// Provider\nexport function GameProvider({\n  children\n}) {\n  _s();\n  const [state, dispatch] = useReducer(gameReducer, initialState);\n  return /*#__PURE__*/_jsxDEV(GameContext.Provider, {\n    value: {\n      state,\n      dispatch\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n}\n\n// Hook\n_s(GameProvider, \"6JWkGZ32UPfojeNx+xqn8ZU8A0Q=\");\n_c = GameProvider;\nexport function useGame() {\n  _s2();\n  const context = useContext(GameContext);\n  if (!context) {\n    throw new Error('useGame must be used within a GameProvider');\n  }\n  return context;\n}\n_s2(useGame, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"GameProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "jsxDEV", "_jsxDEV", "initialState", "user", "id", "username", "vipLevel", "diamonds", "coins", "energy", "maxEnergy", "characters", "currentCharacter", "inventory", "currentScene", "battleState", "isInBattle", "enemy", "battleLog", "gameReducer", "state", "action", "_state$currentCharact", "_action$payload$diamo", "_action$payload$coins", "type", "payload", "map", "char", "GameContext", "GameProvider", "children", "_s", "dispatch", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useGame", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/context/GameContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, ReactNode } from 'react';\n\n// 游戏状态接口\nexport interface Character {\n  id: string;\n  name: string;\n  level: number;\n  exp: number;\n  maxExp: number;\n  hp: number;\n  maxHp: number;\n  mp: number;\n  maxMp: number;\n  attack: number;\n  defense: number;\n  speed: number;\n  soulPower: number;\n  soulRings: SoulRing[];\n  equipment: Equipment[];\n  avatar: string;\n}\n\nexport interface SoulRing {\n  id: string;\n  name: string;\n  age: number; // 魂环年限\n  type: 'white' | 'yellow' | 'purple' | 'black' | 'red';\n  skills: string[];\n}\n\nexport interface Equipment {\n  id: string;\n  name: string;\n  type: 'weapon' | 'armor' | 'accessory';\n  level: number;\n  stats: {\n    attack?: number;\n    defense?: number;\n    hp?: number;\n    mp?: number;\n  };\n}\n\nexport interface GameState {\n  user: {\n    id: string;\n    username: string;\n    vipLevel: number;\n    diamonds: number;\n    coins: number;\n    energy: number;\n    maxEnergy: number;\n  };\n  characters: Character[];\n  currentCharacter: Character | null;\n  inventory: any[];\n  currentScene: 'main' | 'battle' | 'shop' | 'character' | 'guild' | 'gacha';\n  battleState: {\n    isInBattle: boolean;\n    enemy: any;\n    battleLog: string[];\n  };\n}\n\n// 动作类型\ntype GameAction =\n  | { type: 'SET_USER'; payload: any }\n  | { type: 'SET_CURRENT_CHARACTER'; payload: Character }\n  | { type: 'UPDATE_CHARACTER'; payload: Character }\n  | { type: 'ADD_CHARACTER'; payload: Character }\n  | { type: 'SET_SCENE'; payload: string }\n  | { type: 'UPDATE_CURRENCY'; payload: { diamonds?: number; coins?: number } }\n  | { type: 'START_BATTLE'; payload: any }\n  | { type: 'END_BATTLE' }\n  | { type: 'ADD_BATTLE_LOG'; payload: string };\n\n// 初始状态\nconst initialState: GameState = {\n  user: {\n    id: '',\n    username: '',\n    vipLevel: 0,\n    diamonds: 100,\n    coins: 1000,\n    energy: 100,\n    maxEnergy: 100,\n  },\n  characters: [],\n  currentCharacter: null,\n  inventory: [],\n  currentScene: 'main',\n  battleState: {\n    isInBattle: false,\n    enemy: null,\n    battleLog: [],\n  },\n};\n\n// Reducer\nfunction gameReducer(state: GameState, action: GameAction): GameState {\n  switch (action.type) {\n    case 'SET_USER':\n      return {\n        ...state,\n        user: { ...state.user, ...action.payload },\n      };\n    case 'SET_CURRENT_CHARACTER':\n      return {\n        ...state,\n        currentCharacter: action.payload,\n      };\n    case 'UPDATE_CHARACTER':\n      return {\n        ...state,\n        characters: state.characters.map(char =>\n          char.id === action.payload.id ? action.payload : char\n        ),\n        currentCharacter: state.currentCharacter?.id === action.payload.id \n          ? action.payload \n          : state.currentCharacter,\n      };\n    case 'ADD_CHARACTER':\n      return {\n        ...state,\n        characters: [...state.characters, action.payload],\n      };\n    case 'SET_SCENE':\n      return {\n        ...state,\n        currentScene: action.payload as any,\n      };\n    case 'UPDATE_CURRENCY':\n      return {\n        ...state,\n        user: {\n          ...state.user,\n          diamonds: action.payload.diamonds ?? state.user.diamonds,\n          coins: action.payload.coins ?? state.user.coins,\n        },\n      };\n    case 'START_BATTLE':\n      return {\n        ...state,\n        battleState: {\n          isInBattle: true,\n          enemy: action.payload,\n          battleLog: [],\n        },\n      };\n    case 'END_BATTLE':\n      return {\n        ...state,\n        battleState: {\n          ...state.battleState,\n          isInBattle: false,\n          enemy: null,\n        },\n      };\n    case 'ADD_BATTLE_LOG':\n      return {\n        ...state,\n        battleState: {\n          ...state.battleState,\n          battleLog: [...state.battleState.battleLog, action.payload],\n        },\n      };\n    default:\n      return state;\n  }\n}\n\n// Context\nconst GameContext = createContext<{\n  state: GameState;\n  dispatch: React.Dispatch<GameAction>;\n} | null>(null);\n\n// Provider\nexport function GameProvider({ children }: { children: ReactNode }) {\n  const [state, dispatch] = useReducer(gameReducer, initialState);\n\n  return (\n    <GameContext.Provider value={{ state, dispatch }}>\n      {children}\n    </GameContext.Provider>\n  );\n}\n\n// Hook\nexport function useGame() {\n  const context = useContext(GameContext);\n  if (!context) {\n    throw new Error('useGame must be used within a GameProvider');\n  }\n  return context;\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,QAAmB,OAAO;;AAE/E;;AA8DA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAYA;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE;IACJC,EAAE,EAAE,EAAE;IACNC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,GAAG;IACbC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,GAAG;IACXC,SAAS,EAAE;EACb,CAAC;EACDC,UAAU,EAAE,EAAE;EACdC,gBAAgB,EAAE,IAAI;EACtBC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,MAAM;EACpBC,WAAW,EAAE;IACXC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE;EACb;AACF,CAAC;;AAED;AACA,SAASC,WAAWA,CAACC,KAAgB,EAAEC,MAAkB,EAAa;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACpE,QAAQH,MAAM,CAACI,IAAI;IACjB,KAAK,UAAU;MACb,OAAO;QACL,GAAGL,KAAK;QACRjB,IAAI,EAAE;UAAE,GAAGiB,KAAK,CAACjB,IAAI;UAAE,GAAGkB,MAAM,CAACK;QAAQ;MAC3C,CAAC;IACH,KAAK,uBAAuB;MAC1B,OAAO;QACL,GAAGN,KAAK;QACRR,gBAAgB,EAAES,MAAM,CAACK;MAC3B,CAAC;IACH,KAAK,kBAAkB;MACrB,OAAO;QACL,GAAGN,KAAK;QACRT,UAAU,EAAES,KAAK,CAACT,UAAU,CAACgB,GAAG,CAACC,IAAI,IACnCA,IAAI,CAACxB,EAAE,KAAKiB,MAAM,CAACK,OAAO,CAACtB,EAAE,GAAGiB,MAAM,CAACK,OAAO,GAAGE,IACnD,CAAC;QACDhB,gBAAgB,EAAE,EAAAU,qBAAA,GAAAF,KAAK,CAACR,gBAAgB,cAAAU,qBAAA,uBAAtBA,qBAAA,CAAwBlB,EAAE,MAAKiB,MAAM,CAACK,OAAO,CAACtB,EAAE,GAC9DiB,MAAM,CAACK,OAAO,GACdN,KAAK,CAACR;MACZ,CAAC;IACH,KAAK,eAAe;MAClB,OAAO;QACL,GAAGQ,KAAK;QACRT,UAAU,EAAE,CAAC,GAAGS,KAAK,CAACT,UAAU,EAAEU,MAAM,CAACK,OAAO;MAClD,CAAC;IACH,KAAK,WAAW;MACd,OAAO;QACL,GAAGN,KAAK;QACRN,YAAY,EAAEO,MAAM,CAACK;MACvB,CAAC;IACH,KAAK,iBAAiB;MACpB,OAAO;QACL,GAAGN,KAAK;QACRjB,IAAI,EAAE;UACJ,GAAGiB,KAAK,CAACjB,IAAI;UACbI,QAAQ,GAAAgB,qBAAA,GAAEF,MAAM,CAACK,OAAO,CAACnB,QAAQ,cAAAgB,qBAAA,cAAAA,qBAAA,GAAIH,KAAK,CAACjB,IAAI,CAACI,QAAQ;UACxDC,KAAK,GAAAgB,qBAAA,GAAEH,MAAM,CAACK,OAAO,CAAClB,KAAK,cAAAgB,qBAAA,cAAAA,qBAAA,GAAIJ,KAAK,CAACjB,IAAI,CAACK;QAC5C;MACF,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGY,KAAK;QACRL,WAAW,EAAE;UACXC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAEI,MAAM,CAACK,OAAO;UACrBR,SAAS,EAAE;QACb;MACF,CAAC;IACH,KAAK,YAAY;MACf,OAAO;QACL,GAAGE,KAAK;QACRL,WAAW,EAAE;UACX,GAAGK,KAAK,CAACL,WAAW;UACpBC,UAAU,EAAE,KAAK;UACjBC,KAAK,EAAE;QACT;MACF,CAAC;IACH,KAAK,gBAAgB;MACnB,OAAO;QACL,GAAGG,KAAK;QACRL,WAAW,EAAE;UACX,GAAGK,KAAK,CAACL,WAAW;UACpBG,SAAS,EAAE,CAAC,GAAGE,KAAK,CAACL,WAAW,CAACG,SAAS,EAAEG,MAAM,CAACK,OAAO;QAC5D;MACF,CAAC;IACH;MACE,OAAON,KAAK;EAChB;AACF;;AAEA;AACA,MAAMS,WAAW,gBAAGhC,aAAa,CAGvB,IAAI,CAAC;;AAEf;AACA,OAAO,SAASiC,YAAYA,CAAC;EAAEC;AAAkC,CAAC,EAAE;EAAAC,EAAA;EAClE,MAAM,CAACZ,KAAK,EAAEa,QAAQ,CAAC,GAAGlC,UAAU,CAACoB,WAAW,EAAEjB,YAAY,CAAC;EAE/D,oBACED,OAAA,CAAC4B,WAAW,CAACK,QAAQ;IAACC,KAAK,EAAE;MAAEf,KAAK;MAAEa;IAAS,CAAE;IAAAF,QAAA,EAC9CA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B;;AAEA;AAAAP,EAAA,CAVgBF,YAAY;AAAAU,EAAA,GAAZV,YAAY;AAW5B,OAAO,SAASW,OAAOA,CAAA,EAAG;EAAAC,GAAA;EACxB,MAAMC,OAAO,GAAG7C,UAAU,CAAC+B,WAAW,CAAC;EACvC,IAAI,CAACc,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB;AAACD,GAAA,CANeD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}