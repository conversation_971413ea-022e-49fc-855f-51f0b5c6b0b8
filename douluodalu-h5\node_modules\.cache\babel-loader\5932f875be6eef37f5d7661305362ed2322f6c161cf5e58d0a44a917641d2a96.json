{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\components\\\\ShopPanel.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ShopContainer = styled.div`\n  flex: 1;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n`;\n_c = ShopContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n`;\n_c2 = Header;\nconst Title = styled.h2`\n  color: #ffd700;\n  margin: 0;\n`;\n_c3 = Title;\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #ffd700;\n  color: #ffd700;\n  padding: 10px 20px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n  }\n`;\n_c4 = BackButton;\nconst TabContainer = styled.div`\n  display: flex;\n  gap: 10px;\n  margin-bottom: 30px;\n`;\n_c5 = TabContainer;\nconst Tab = styled.button`\n  padding: 12px 24px;\n  border: 2px solid #ffd700;\n  background: ${props => props.active ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.1)'};\n  color: #ffd700;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n  }\n`;\n_c6 = Tab;\nconst ShopGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n  flex: 1;\n`;\n_c7 = ShopGrid;\nconst ItemCard = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid #ffd700;\n  border-radius: 15px;\n  padding: 20px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);\n  }\n`;\n_c8 = ItemCard;\nconst ItemIcon = styled.div`\n  font-size: 60px;\n  text-align: center;\n  margin-bottom: 15px;\n`;\n_c9 = ItemIcon;\nconst ItemName = styled.h3`\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 10px;\n  font-size: 1.2rem;\n`;\n_c0 = ItemName;\nconst ItemDescription = styled.p`\n  color: #fff;\n  font-size: 14px;\n  margin-bottom: 15px;\n  text-align: center;\n  opacity: 0.9;\n`;\n_c1 = ItemDescription;\nconst ItemStats = styled.div`\n  margin-bottom: 15px;\n`;\n_c10 = ItemStats;\nconst StatLine = styled.div`\n  display: flex;\n  justify-content: space-between;\n  color: #4ecdc4;\n  font-size: 14px;\n  margin-bottom: 5px;\n`;\n_c11 = StatLine;\nconst PriceContainer = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n`;\n_c12 = PriceContainer;\nconst Price = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  color: #ffd700;\n  font-weight: bold;\n`;\n_c13 = Price;\nconst BuyButton = styled.button`\n  width: 100%;\n  padding: 12px;\n  border: none;\n  border-radius: 8px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.canAfford ? `\n    background: linear-gradient(45deg, #2ecc71, #27ae60);\n    color: white;\n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);\n    }\n  ` : `\n    background: rgba(255, 255, 255, 0.1);\n    color: #666;\n    cursor: not-allowed;\n  `}\n`;\n_c14 = BuyButton;\nconst shopItems = {\n  equipment: [{\n    id: 'sword_1',\n    name: '铁剑',\n    type: 'weapon',\n    icon: '⚔️',\n    description: '基础的铁制长剑，攻击力+10',\n    stats: {\n      attack: 10\n    },\n    price: {\n      coins: 100\n    },\n    level: 1\n  }, {\n    id: 'armor_1',\n    name: '皮甲',\n    type: 'armor',\n    icon: '🛡️',\n    description: '轻便的皮制护甲，防御力+8',\n    stats: {\n      defense: 8\n    },\n    price: {\n      coins: 80\n    },\n    level: 1\n  }, {\n    id: 'ring_1',\n    name: '力量戒指',\n    type: 'accessory',\n    icon: '💍',\n    description: '增强力量的魔法戒指，攻击力+5，生命值+20',\n    stats: {\n      attack: 5,\n      hp: 20\n    },\n    price: {\n      coins: 150\n    },\n    level: 1\n  }, {\n    id: 'sword_2',\n    name: '钢剑',\n    type: 'weapon',\n    icon: '⚔️',\n    description: '锋利的钢制长剑，攻击力+20',\n    stats: {\n      attack: 20\n    },\n    price: {\n      diamonds: 50\n    },\n    level: 2\n  }],\n  consumables: [{\n    id: 'hp_potion',\n    name: '生命药水',\n    icon: '🧪',\n    description: '恢复50点生命值',\n    effect: {\n      hp: 50\n    },\n    price: {\n      coins: 20\n    },\n    stackable: true\n  }, {\n    id: 'mp_potion',\n    name: '魂力药水',\n    icon: '💙',\n    description: '恢复30点魂力值',\n    effect: {\n      mp: 30\n    },\n    price: {\n      coins: 25\n    },\n    stackable: true\n  }, {\n    id: 'exp_boost',\n    name: '经验卷轴',\n    icon: '📜',\n    description: '获得100点经验值',\n    effect: {\n      exp: 100\n    },\n    price: {\n      diamonds: 10\n    },\n    stackable: true\n  }],\n  special: [{\n    id: 'soul_ring_white',\n    name: '白色魂环',\n    icon: '⭕',\n    description: '10年魂环，提升基础属性',\n    stats: {\n      attack: 3,\n      defense: 2,\n      hp: 15\n    },\n    price: {\n      diamonds: 100\n    },\n    rarity: 'white'\n  }, {\n    id: 'soul_ring_yellow',\n    name: '黄色魂环',\n    icon: '🟡',\n    description: '100年魂环，大幅提升属性',\n    stats: {\n      attack: 8,\n      defense: 5,\n      hp: 30\n    },\n    price: {\n      diamonds: 300\n    },\n    rarity: 'yellow'\n  }]\n};\nconst ShopPanel = () => {\n  _s();\n  const {\n    state,\n    dispatch\n  } = useGame();\n  const [activeTab, setActiveTab] = useState('equipment');\n  const handleBack = () => {\n    dispatch({\n      type: 'SET_SCENE',\n      payload: 'main'\n    });\n  };\n  const canAfford = price => {\n    if (price.coins && state.user.coins < price.coins) return false;\n    if (price.diamonds && state.user.diamonds < price.diamonds) return false;\n    return true;\n  };\n  const buyItem = item => {\n    if (!canAfford(item.price)) {\n      alert('金币或钻石不足！');\n      return;\n    }\n\n    // 扣除货币\n    const newCurrency = {};\n    if (item.price.coins) {\n      newCurrency.coins = -item.price.coins;\n    }\n    if (item.price.diamonds) {\n      newCurrency.diamonds = -item.price.diamonds;\n    }\n    dispatch({\n      type: 'UPDATE_CURRENCY',\n      payload: newCurrency\n    });\n\n    // 处理不同类型的物品\n    if (activeTab === 'equipment') {\n      // 装备物品 - 这里简化处理，实际应该添加到背包\n      alert(`购买成功！获得 ${item.name}`);\n    } else if (activeTab === 'consumables') {\n      // 消耗品 - 直接使用\n      if (state.currentCharacter) {\n        const updatedCharacter = {\n          ...state.currentCharacter\n        };\n        if (item.effect.hp) {\n          updatedCharacter.hp = Math.min(updatedCharacter.maxHp, updatedCharacter.hp + item.effect.hp);\n        }\n        if (item.effect.mp) {\n          updatedCharacter.mp = Math.min(updatedCharacter.maxMp, updatedCharacter.mp + item.effect.mp);\n        }\n        if (item.effect.exp) {\n          updatedCharacter.exp += item.effect.exp;\n\n          // 检查升级\n          while (updatedCharacter.exp >= updatedCharacter.maxExp) {\n            updatedCharacter.level += 1;\n            updatedCharacter.exp -= updatedCharacter.maxExp;\n            updatedCharacter.maxExp = Math.floor(updatedCharacter.maxExp * 1.2);\n            updatedCharacter.maxHp += 20;\n            updatedCharacter.maxMp += 10;\n            updatedCharacter.attack += 5;\n            updatedCharacter.defense += 3;\n          }\n        }\n        dispatch({\n          type: 'UPDATE_CHARACTER',\n          payload: updatedCharacter\n        });\n        alert(`使用成功！${item.description}`);\n      }\n    } else if (activeTab === 'special') {\n      // 特殊物品 - 魂环等\n      if (state.currentCharacter && item.rarity) {\n        const newSoulRing = {\n          id: `ring_${Date.now()}`,\n          name: item.name,\n          age: item.rarity === 'white' ? 10 : 100,\n          type: item.rarity,\n          skills: []\n        };\n        const updatedCharacter = {\n          ...state.currentCharacter,\n          soulRings: [...state.currentCharacter.soulRings, newSoulRing],\n          attack: state.currentCharacter.attack + (item.stats.attack || 0),\n          defense: state.currentCharacter.defense + (item.stats.defense || 0),\n          maxHp: state.currentCharacter.maxHp + (item.stats.hp || 0),\n          hp: state.currentCharacter.hp + (item.stats.hp || 0)\n        };\n        dispatch({\n          type: 'UPDATE_CHARACTER',\n          payload: updatedCharacter\n        });\n        alert(`获得魂环！${item.name} 已装备`);\n      }\n    }\n  };\n  const renderItems = () => {\n    const items = shopItems[activeTab];\n    return items.map(item => /*#__PURE__*/_jsxDEV(ItemCard, {\n      children: [/*#__PURE__*/_jsxDEV(ItemIcon, {\n        children: item.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ItemName, {\n        children: item.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ItemDescription, {\n        children: item.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), item.stats && /*#__PURE__*/_jsxDEV(ItemStats, {\n        children: Object.entries(item.stats).map(([stat, value]) => /*#__PURE__*/_jsxDEV(StatLine, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: stat === 'attack' ? '攻击力' : stat === 'defense' ? '防御力' : stat === 'hp' ? '生命值' : stat\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"+\", value]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 17\n          }, this)]\n        }, stat, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(PriceContainer, {\n        children: /*#__PURE__*/_jsxDEV(Price, {\n          children: [item.price.coins && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83E\\uDE99\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.price.coins\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), item.price.diamonds && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83D\\uDC8E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.price.diamonds\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BuyButton, {\n        canAfford: canAfford(item.price),\n        onClick: () => buyItem(item),\n        disabled: !canAfford(item.price),\n        children: canAfford(item.price) ? '购买' : '金币不足'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)]\n    }, item.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(ShopContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\u5546\\u5E97\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BackButton, {\n        onClick: handleBack,\n        children: \"\\u8FD4\\u56DE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Tab, {\n        active: activeTab === 'equipment',\n        onClick: () => setActiveTab('equipment'),\n        children: \"\\u88C5\\u5907\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        active: activeTab === 'consumables',\n        onClick: () => setActiveTab('consumables'),\n        children: \"\\u6D88\\u8017\\u54C1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tab, {\n        active: activeTab === 'special',\n        onClick: () => setActiveTab('special'),\n        children: \"\\u7279\\u6B8A\\u7269\\u54C1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ShopGrid, {\n      children: renderItems()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 389,\n    columnNumber: 5\n  }, this);\n};\n_s(ShopPanel, \"lvC/k32mZdZgaspMIesXc/1yo4M=\", false, function () {\n  return [useGame];\n});\n_c15 = ShopPanel;\nexport default ShopPanel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"ShopContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"BackButton\");\n$RefreshReg$(_c5, \"TabContainer\");\n$RefreshReg$(_c6, \"Tab\");\n$RefreshReg$(_c7, \"ShopGrid\");\n$RefreshReg$(_c8, \"ItemCard\");\n$RefreshReg$(_c9, \"ItemIcon\");\n$RefreshReg$(_c0, \"ItemName\");\n$RefreshReg$(_c1, \"ItemDescription\");\n$RefreshReg$(_c10, \"ItemStats\");\n$RefreshReg$(_c11, \"StatLine\");\n$RefreshReg$(_c12, \"PriceContainer\");\n$RefreshReg$(_c13, \"Price\");\n$RefreshReg$(_c14, \"BuyButton\");\n$RefreshReg$(_c15, \"ShopPanel\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useGame", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ShopContainer", "div", "_c", "Header", "_c2", "Title", "h2", "_c3", "BackButton", "button", "_c4", "TabContainer", "_c5", "Tab", "props", "active", "_c6", "ShopGrid", "_c7", "ItemCard", "_c8", "ItemIcon", "_c9", "ItemName", "h3", "_c0", "ItemDescription", "p", "_c1", "ItemStats", "_c10", "StatLine", "_c11", "PriceContainer", "_c12", "Price", "_c13", "BuyButton", "can<PERSON>fford", "_c14", "shopItems", "equipment", "id", "name", "type", "icon", "description", "stats", "attack", "price", "coins", "level", "defense", "hp", "diamonds", "consumables", "effect", "stackable", "mp", "exp", "special", "rarity", "ShopPanel", "_s", "state", "dispatch", "activeTab", "setActiveTab", "handleBack", "payload", "user", "buyItem", "item", "alert", "newCurrency", "currentCharacter", "updatedCharacter", "Math", "min", "maxHp", "maxMp", "maxExp", "floor", "newSoulRing", "Date", "now", "age", "skills", "soulRings", "renderItems", "items", "map", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Object", "entries", "stat", "value", "onClick", "disabled", "_c15", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/components/ShopPanel.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\n\nconst ShopContainer = styled.div`\n  flex: 1;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n`;\n\nconst Title = styled.h2`\n  color: #ffd700;\n  margin: 0;\n`;\n\nconst BackButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid #ffd700;\n  color: #ffd700;\n  padding: 10px 20px;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n  }\n`;\n\nconst TabContainer = styled.div`\n  display: flex;\n  gap: 10px;\n  margin-bottom: 30px;\n`;\n\nconst Tab = styled.button<{ active?: boolean }>`\n  padding: 12px 24px;\n  border: 2px solid #ffd700;\n  background: ${props => props.active ? 'rgba(255, 215, 0, 0.3)' : 'rgba(255, 255, 255, 0.1)'};\n  color: #ffd700;\n  border-radius: 10px;\n  cursor: pointer;\n  font-weight: bold;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    background: rgba(255, 215, 0, 0.2);\n  }\n`;\n\nconst ShopGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n  flex: 1;\n`;\n\nconst ItemCard = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border: 2px solid #ffd700;\n  border-radius: 15px;\n  padding: 20px;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);\n  }\n`;\n\nconst ItemIcon = styled.div`\n  font-size: 60px;\n  text-align: center;\n  margin-bottom: 15px;\n`;\n\nconst ItemName = styled.h3`\n  color: #ffd700;\n  text-align: center;\n  margin-bottom: 10px;\n  font-size: 1.2rem;\n`;\n\nconst ItemDescription = styled.p`\n  color: #fff;\n  font-size: 14px;\n  margin-bottom: 15px;\n  text-align: center;\n  opacity: 0.9;\n`;\n\nconst ItemStats = styled.div`\n  margin-bottom: 15px;\n`;\n\nconst StatLine = styled.div`\n  display: flex;\n  justify-content: space-between;\n  color: #4ecdc4;\n  font-size: 14px;\n  margin-bottom: 5px;\n`;\n\nconst PriceContainer = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n`;\n\nconst Price = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  color: #ffd700;\n  font-weight: bold;\n`;\n\nconst BuyButton = styled.button<{ canAfford?: boolean }>`\n  width: 100%;\n  padding: 12px;\n  border: none;\n  border-radius: 8px;\n  font-weight: bold;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.canAfford ? `\n    background: linear-gradient(45deg, #2ecc71, #27ae60);\n    color: white;\n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);\n    }\n  ` : `\n    background: rgba(255, 255, 255, 0.1);\n    color: #666;\n    cursor: not-allowed;\n  `}\n`;\n\nconst shopItems = {\n  equipment: [\n    {\n      id: 'sword_1',\n      name: '铁剑',\n      type: 'weapon',\n      icon: '⚔️',\n      description: '基础的铁制长剑，攻击力+10',\n      stats: { attack: 10 },\n      price: { coins: 100 },\n      level: 1,\n    },\n    {\n      id: 'armor_1',\n      name: '皮甲',\n      type: 'armor',\n      icon: '🛡️',\n      description: '轻便的皮制护甲，防御力+8',\n      stats: { defense: 8 },\n      price: { coins: 80 },\n      level: 1,\n    },\n    {\n      id: 'ring_1',\n      name: '力量戒指',\n      type: 'accessory',\n      icon: '💍',\n      description: '增强力量的魔法戒指，攻击力+5，生命值+20',\n      stats: { attack: 5, hp: 20 },\n      price: { coins: 150 },\n      level: 1,\n    },\n    {\n      id: 'sword_2',\n      name: '钢剑',\n      type: 'weapon',\n      icon: '⚔️',\n      description: '锋利的钢制长剑，攻击力+20',\n      stats: { attack: 20 },\n      price: { diamonds: 50 },\n      level: 2,\n    },\n  ],\n  consumables: [\n    {\n      id: 'hp_potion',\n      name: '生命药水',\n      icon: '🧪',\n      description: '恢复50点生命值',\n      effect: { hp: 50 },\n      price: { coins: 20 },\n      stackable: true,\n    },\n    {\n      id: 'mp_potion',\n      name: '魂力药水',\n      icon: '💙',\n      description: '恢复30点魂力值',\n      effect: { mp: 30 },\n      price: { coins: 25 },\n      stackable: true,\n    },\n    {\n      id: 'exp_boost',\n      name: '经验卷轴',\n      icon: '📜',\n      description: '获得100点经验值',\n      effect: { exp: 100 },\n      price: { diamonds: 10 },\n      stackable: true,\n    },\n  ],\n  special: [\n    {\n      id: 'soul_ring_white',\n      name: '白色魂环',\n      icon: '⭕',\n      description: '10年魂环，提升基础属性',\n      stats: { attack: 3, defense: 2, hp: 15 },\n      price: { diamonds: 100 },\n      rarity: 'white',\n    },\n    {\n      id: 'soul_ring_yellow',\n      name: '黄色魂环',\n      icon: '🟡',\n      description: '100年魂环，大幅提升属性',\n      stats: { attack: 8, defense: 5, hp: 30 },\n      price: { diamonds: 300 },\n      rarity: 'yellow',\n    },\n  ],\n};\n\nconst ShopPanel: React.FC = () => {\n  const { state, dispatch } = useGame();\n  const [activeTab, setActiveTab] = useState<'equipment' | 'consumables' | 'special'>('equipment');\n\n  const handleBack = () => {\n    dispatch({ type: 'SET_SCENE', payload: 'main' });\n  };\n\n  const canAfford = (price: any) => {\n    if (price.coins && state.user.coins < price.coins) return false;\n    if (price.diamonds && state.user.diamonds < price.diamonds) return false;\n    return true;\n  };\n\n  const buyItem = (item: any) => {\n    if (!canAfford(item.price)) {\n      alert('金币或钻石不足！');\n      return;\n    }\n\n    // 扣除货币\n    const newCurrency: any = {};\n    if (item.price.coins) {\n      newCurrency.coins = -item.price.coins;\n    }\n    if (item.price.diamonds) {\n      newCurrency.diamonds = -item.price.diamonds;\n    }\n    dispatch({ type: 'UPDATE_CURRENCY', payload: newCurrency });\n\n    // 处理不同类型的物品\n    if (activeTab === 'equipment') {\n      // 装备物品 - 这里简化处理，实际应该添加到背包\n      alert(`购买成功！获得 ${item.name}`);\n    } else if (activeTab === 'consumables') {\n      // 消耗品 - 直接使用\n      if (state.currentCharacter) {\n        const updatedCharacter = { ...state.currentCharacter };\n        \n        if (item.effect.hp) {\n          updatedCharacter.hp = Math.min(\n            updatedCharacter.maxHp,\n            updatedCharacter.hp + item.effect.hp\n          );\n        }\n        if (item.effect.mp) {\n          updatedCharacter.mp = Math.min(\n            updatedCharacter.maxMp,\n            updatedCharacter.mp + item.effect.mp\n          );\n        }\n        if (item.effect.exp) {\n          updatedCharacter.exp += item.effect.exp;\n          \n          // 检查升级\n          while (updatedCharacter.exp >= updatedCharacter.maxExp) {\n            updatedCharacter.level += 1;\n            updatedCharacter.exp -= updatedCharacter.maxExp;\n            updatedCharacter.maxExp = Math.floor(updatedCharacter.maxExp * 1.2);\n            updatedCharacter.maxHp += 20;\n            updatedCharacter.maxMp += 10;\n            updatedCharacter.attack += 5;\n            updatedCharacter.defense += 3;\n          }\n        }\n        \n        dispatch({ type: 'UPDATE_CHARACTER', payload: updatedCharacter });\n        alert(`使用成功！${item.description}`);\n      }\n    } else if (activeTab === 'special') {\n      // 特殊物品 - 魂环等\n      if (state.currentCharacter && item.rarity) {\n        const newSoulRing = {\n          id: `ring_${Date.now()}`,\n          name: item.name,\n          age: item.rarity === 'white' ? 10 : 100,\n          type: item.rarity,\n          skills: [],\n        };\n        \n        const updatedCharacter = {\n          ...state.currentCharacter,\n          soulRings: [...state.currentCharacter.soulRings, newSoulRing],\n          attack: state.currentCharacter.attack + (item.stats.attack || 0),\n          defense: state.currentCharacter.defense + (item.stats.defense || 0),\n          maxHp: state.currentCharacter.maxHp + (item.stats.hp || 0),\n          hp: state.currentCharacter.hp + (item.stats.hp || 0),\n        };\n        \n        dispatch({ type: 'UPDATE_CHARACTER', payload: updatedCharacter });\n        alert(`获得魂环！${item.name} 已装备`);\n      }\n    }\n  };\n\n  const renderItems = () => {\n    const items = shopItems[activeTab];\n    \n    return items.map((item: any) => (\n      <ItemCard key={item.id}>\n        <ItemIcon>{item.icon}</ItemIcon>\n        <ItemName>{item.name}</ItemName>\n        <ItemDescription>{item.description}</ItemDescription>\n        \n        {item.stats && (\n          <ItemStats>\n            {Object.entries(item.stats).map(([stat, value]: [string, any]) => (\n              <StatLine key={stat}>\n                <span>{stat === 'attack' ? '攻击力' : stat === 'defense' ? '防御力' : stat === 'hp' ? '生命值' : stat}</span>\n                <span>+{value}</span>\n              </StatLine>\n            ))}\n          </ItemStats>\n        )}\n        \n        <PriceContainer>\n          <Price>\n            {item.price.coins && (\n              <>\n                <span>🪙</span>\n                <span>{item.price.coins}</span>\n              </>\n            )}\n            {item.price.diamonds && (\n              <>\n                <span>💎</span>\n                <span>{item.price.diamonds}</span>\n              </>\n            )}\n          </Price>\n        </PriceContainer>\n        \n        <BuyButton \n          canAfford={canAfford(item.price)}\n          onClick={() => buyItem(item)}\n          disabled={!canAfford(item.price)}\n        >\n          {canAfford(item.price) ? '购买' : '金币不足'}\n        </BuyButton>\n      </ItemCard>\n    ));\n  };\n\n  return (\n    <ShopContainer>\n      <Header>\n        <Title>商店</Title>\n        <BackButton onClick={handleBack}>返回</BackButton>\n      </Header>\n\n      <TabContainer>\n        <Tab \n          active={activeTab === 'equipment'} \n          onClick={() => setActiveTab('equipment')}\n        >\n          装备\n        </Tab>\n        <Tab \n          active={activeTab === 'consumables'} \n          onClick={() => setActiveTab('consumables')}\n        >\n          消耗品\n        </Tab>\n        <Tab \n          active={activeTab === 'special'} \n          onClick={() => setActiveTab('special')}\n        >\n          特殊物品\n        </Tab>\n      </TabContainer>\n\n      <ShopGrid>\n        {renderItems()}\n      </ShopGrid>\n    </ShopContainer>\n  );\n};\n\nexport default ShopPanel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,aAAa,GAAGN,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,aAAa;AAOnB,MAAMG,MAAM,GAAGT,MAAM,CAACO,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,MAAM;AAOZ,MAAME,KAAK,GAAGX,MAAM,CAACY,EAAE;AACvB;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,KAAK;AAKX,MAAMG,UAAU,GAAGd,MAAM,CAACe,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,UAAU;AAehB,MAAMG,YAAY,GAAGjB,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,YAAY;AAMlB,MAAME,GAAG,GAAGnB,MAAM,CAACe,MAA4B;AAC/C;AACA;AACA,gBAAgBK,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,wBAAwB,GAAG,0BAA0B;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIH,GAAG;AAeT,MAAMI,QAAQ,GAAGvB,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GALID,QAAQ;AAOd,MAAME,QAAQ,GAAGzB,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAXID,QAAQ;AAad,MAAME,QAAQ,GAAG3B,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAG7B,MAAM,CAAC8B,EAAE;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,QAAQ;AAOd,MAAMG,eAAe,GAAGhC,MAAM,CAACiC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,eAAe;AAQrB,MAAMG,SAAS,GAAGnC,MAAM,CAACO,GAAG;AAC5B;AACA,CAAC;AAAC6B,IAAA,GAFID,SAAS;AAIf,MAAME,QAAQ,GAAGrC,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GANID,QAAQ;AAQd,MAAME,cAAc,GAAGvC,MAAM,CAACO,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACiC,IAAA,GALID,cAAc;AAOpB,MAAME,KAAK,GAAGzC,MAAM,CAACO,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GANID,KAAK;AAQX,MAAME,SAAS,GAAG3C,MAAM,CAACe,MAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,KAAK,IAAIA,KAAK,CAACwB,SAAS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACC,IAAA,GArBIF,SAAS;AAuBf,MAAMG,SAAS,GAAG;EAChBC,SAAS,EAAE,CACT;IACEC,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC;IACrBC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,KAAK;IACXC,WAAW,EAAE,eAAe;IAC5BC,KAAK,EAAE;MAAEK,OAAO,EAAE;IAAE,CAAC;IACrBH,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC;IACpBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,wBAAwB;IACrCC,KAAK,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEK,EAAE,EAAE;IAAG,CAAC;IAC5BJ,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC;IACrBC,KAAK,EAAE;MAAEK,QAAQ,EAAE;IAAG,CAAC;IACvBH,KAAK,EAAE;EACT,CAAC,CACF;EACDI,WAAW,EAAE,CACX;IACEb,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,MAAM;IACZE,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,UAAU;IACvBU,MAAM,EAAE;MAAEH,EAAE,EAAE;IAAG,CAAC;IAClBJ,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC;IACpBO,SAAS,EAAE;EACb,CAAC,EACD;IACEf,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,MAAM;IACZE,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,UAAU;IACvBU,MAAM,EAAE;MAAEE,EAAE,EAAE;IAAG,CAAC;IAClBT,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC;IACpBO,SAAS,EAAE;EACb,CAAC,EACD;IACEf,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,MAAM;IACZE,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,WAAW;IACxBU,MAAM,EAAE;MAAEG,GAAG,EAAE;IAAI,CAAC;IACpBV,KAAK,EAAE;MAAEK,QAAQ,EAAE;IAAG,CAAC;IACvBG,SAAS,EAAE;EACb,CAAC,CACF;EACDG,OAAO,EAAE,CACP;IACElB,EAAE,EAAE,iBAAiB;IACrBC,IAAI,EAAE,MAAM;IACZE,IAAI,EAAE,GAAG;IACTC,WAAW,EAAE,cAAc;IAC3BC,KAAK,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEI,OAAO,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAG,CAAC;IACxCJ,KAAK,EAAE;MAAEK,QAAQ,EAAE;IAAI,CAAC;IACxBO,MAAM,EAAE;EACV,CAAC,EACD;IACEnB,EAAE,EAAE,kBAAkB;IACtBC,IAAI,EAAE,MAAM;IACZE,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,eAAe;IAC5BC,KAAK,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEI,OAAO,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAG,CAAC;IACxCJ,KAAK,EAAE;MAAEK,QAAQ,EAAE;IAAI,CAAC;IACxBO,MAAM,EAAE;EACV,CAAC;AAEL,CAAC;AAED,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAGtE,OAAO,CAAC,CAAC;EACrC,MAAM,CAACuE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAA0C,WAAW,CAAC;EAEhG,MAAM2E,UAAU,GAAGA,CAAA,KAAM;IACvBH,QAAQ,CAAC;MAAErB,IAAI,EAAE,WAAW;MAAEyB,OAAO,EAAE;IAAO,CAAC,CAAC;EAClD,CAAC;EAED,MAAM/B,SAAS,GAAIW,KAAU,IAAK;IAChC,IAAIA,KAAK,CAACC,KAAK,IAAIc,KAAK,CAACM,IAAI,CAACpB,KAAK,GAAGD,KAAK,CAACC,KAAK,EAAE,OAAO,KAAK;IAC/D,IAAID,KAAK,CAACK,QAAQ,IAAIU,KAAK,CAACM,IAAI,CAAChB,QAAQ,GAAGL,KAAK,CAACK,QAAQ,EAAE,OAAO,KAAK;IACxE,OAAO,IAAI;EACb,CAAC;EAED,MAAMiB,OAAO,GAAIC,IAAS,IAAK;IAC7B,IAAI,CAAClC,SAAS,CAACkC,IAAI,CAACvB,KAAK,CAAC,EAAE;MAC1BwB,KAAK,CAAC,UAAU,CAAC;MACjB;IACF;;IAEA;IACA,MAAMC,WAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIF,IAAI,CAACvB,KAAK,CAACC,KAAK,EAAE;MACpBwB,WAAW,CAACxB,KAAK,GAAG,CAACsB,IAAI,CAACvB,KAAK,CAACC,KAAK;IACvC;IACA,IAAIsB,IAAI,CAACvB,KAAK,CAACK,QAAQ,EAAE;MACvBoB,WAAW,CAACpB,QAAQ,GAAG,CAACkB,IAAI,CAACvB,KAAK,CAACK,QAAQ;IAC7C;IACAW,QAAQ,CAAC;MAAErB,IAAI,EAAE,iBAAiB;MAAEyB,OAAO,EAAEK;IAAY,CAAC,CAAC;;IAE3D;IACA,IAAIR,SAAS,KAAK,WAAW,EAAE;MAC7B;MACAO,KAAK,CAAC,WAAWD,IAAI,CAAC7B,IAAI,EAAE,CAAC;IAC/B,CAAC,MAAM,IAAIuB,SAAS,KAAK,aAAa,EAAE;MACtC;MACA,IAAIF,KAAK,CAACW,gBAAgB,EAAE;QAC1B,MAAMC,gBAAgB,GAAG;UAAE,GAAGZ,KAAK,CAACW;QAAiB,CAAC;QAEtD,IAAIH,IAAI,CAAChB,MAAM,CAACH,EAAE,EAAE;UAClBuB,gBAAgB,CAACvB,EAAE,GAAGwB,IAAI,CAACC,GAAG,CAC5BF,gBAAgB,CAACG,KAAK,EACtBH,gBAAgB,CAACvB,EAAE,GAAGmB,IAAI,CAAChB,MAAM,CAACH,EACpC,CAAC;QACH;QACA,IAAImB,IAAI,CAAChB,MAAM,CAACE,EAAE,EAAE;UAClBkB,gBAAgB,CAAClB,EAAE,GAAGmB,IAAI,CAACC,GAAG,CAC5BF,gBAAgB,CAACI,KAAK,EACtBJ,gBAAgB,CAAClB,EAAE,GAAGc,IAAI,CAAChB,MAAM,CAACE,EACpC,CAAC;QACH;QACA,IAAIc,IAAI,CAAChB,MAAM,CAACG,GAAG,EAAE;UACnBiB,gBAAgB,CAACjB,GAAG,IAAIa,IAAI,CAAChB,MAAM,CAACG,GAAG;;UAEvC;UACA,OAAOiB,gBAAgB,CAACjB,GAAG,IAAIiB,gBAAgB,CAACK,MAAM,EAAE;YACtDL,gBAAgB,CAACzB,KAAK,IAAI,CAAC;YAC3ByB,gBAAgB,CAACjB,GAAG,IAAIiB,gBAAgB,CAACK,MAAM;YAC/CL,gBAAgB,CAACK,MAAM,GAAGJ,IAAI,CAACK,KAAK,CAACN,gBAAgB,CAACK,MAAM,GAAG,GAAG,CAAC;YACnEL,gBAAgB,CAACG,KAAK,IAAI,EAAE;YAC5BH,gBAAgB,CAACI,KAAK,IAAI,EAAE;YAC5BJ,gBAAgB,CAAC5B,MAAM,IAAI,CAAC;YAC5B4B,gBAAgB,CAACxB,OAAO,IAAI,CAAC;UAC/B;QACF;QAEAa,QAAQ,CAAC;UAAErB,IAAI,EAAE,kBAAkB;UAAEyB,OAAO,EAAEO;QAAiB,CAAC,CAAC;QACjEH,KAAK,CAAC,QAAQD,IAAI,CAAC1B,WAAW,EAAE,CAAC;MACnC;IACF,CAAC,MAAM,IAAIoB,SAAS,KAAK,SAAS,EAAE;MAClC;MACA,IAAIF,KAAK,CAACW,gBAAgB,IAAIH,IAAI,CAACX,MAAM,EAAE;QACzC,MAAMsB,WAAW,GAAG;UAClBzC,EAAE,EAAE,QAAQ0C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UACxB1C,IAAI,EAAE6B,IAAI,CAAC7B,IAAI;UACf2C,GAAG,EAAEd,IAAI,CAACX,MAAM,KAAK,OAAO,GAAG,EAAE,GAAG,GAAG;UACvCjB,IAAI,EAAE4B,IAAI,CAACX,MAAM;UACjB0B,MAAM,EAAE;QACV,CAAC;QAED,MAAMX,gBAAgB,GAAG;UACvB,GAAGZ,KAAK,CAACW,gBAAgB;UACzBa,SAAS,EAAE,CAAC,GAAGxB,KAAK,CAACW,gBAAgB,CAACa,SAAS,EAAEL,WAAW,CAAC;UAC7DnC,MAAM,EAAEgB,KAAK,CAACW,gBAAgB,CAAC3B,MAAM,IAAIwB,IAAI,CAACzB,KAAK,CAACC,MAAM,IAAI,CAAC,CAAC;UAChEI,OAAO,EAAEY,KAAK,CAACW,gBAAgB,CAACvB,OAAO,IAAIoB,IAAI,CAACzB,KAAK,CAACK,OAAO,IAAI,CAAC,CAAC;UACnE2B,KAAK,EAAEf,KAAK,CAACW,gBAAgB,CAACI,KAAK,IAAIP,IAAI,CAACzB,KAAK,CAACM,EAAE,IAAI,CAAC,CAAC;UAC1DA,EAAE,EAAEW,KAAK,CAACW,gBAAgB,CAACtB,EAAE,IAAImB,IAAI,CAACzB,KAAK,CAACM,EAAE,IAAI,CAAC;QACrD,CAAC;QAEDY,QAAQ,CAAC;UAAErB,IAAI,EAAE,kBAAkB;UAAEyB,OAAO,EAAEO;QAAiB,CAAC,CAAC;QACjEH,KAAK,CAAC,QAAQD,IAAI,CAAC7B,IAAI,MAAM,CAAC;MAChC;IACF;EACF,CAAC;EAED,MAAM8C,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,KAAK,GAAGlD,SAAS,CAAC0B,SAAS,CAAC;IAElC,OAAOwB,KAAK,CAACC,GAAG,CAAEnB,IAAS,iBACzB3E,OAAA,CAACsB,QAAQ;MAAAyE,QAAA,gBACP/F,OAAA,CAACwB,QAAQ;QAAAuE,QAAA,EAAEpB,IAAI,CAAC3B;MAAI;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAChCnG,OAAA,CAAC0B,QAAQ;QAAAqE,QAAA,EAAEpB,IAAI,CAAC7B;MAAI;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eAChCnG,OAAA,CAAC6B,eAAe;QAAAkE,QAAA,EAAEpB,IAAI,CAAC1B;MAAW;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAkB,CAAC,EAEpDxB,IAAI,CAACzB,KAAK,iBACTlD,OAAA,CAACgC,SAAS;QAAA+D,QAAA,EACPK,MAAM,CAACC,OAAO,CAAC1B,IAAI,CAACzB,KAAK,CAAC,CAAC4C,GAAG,CAAC,CAAC,CAACQ,IAAI,EAAEC,KAAK,CAAgB,kBAC3DvG,OAAA,CAACkC,QAAQ;UAAA6D,QAAA,gBACP/F,OAAA;YAAA+F,QAAA,EAAOO,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAGA,IAAI,KAAK,SAAS,GAAG,KAAK,GAAGA,IAAI,KAAK,IAAI,GAAG,KAAK,GAAGA;UAAI;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpGnG,OAAA;YAAA+F,QAAA,GAAM,GAAC,EAACQ,KAAK;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAFRG,IAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGT,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CACZ,eAEDnG,OAAA,CAACoC,cAAc;QAAA2D,QAAA,eACb/F,OAAA,CAACsC,KAAK;UAAAyD,QAAA,GACHpB,IAAI,CAACvB,KAAK,CAACC,KAAK,iBACfrD,OAAA,CAAAE,SAAA;YAAA6F,QAAA,gBACE/F,OAAA;cAAA+F,QAAA,EAAM;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACfnG,OAAA;cAAA+F,QAAA,EAAOpB,IAAI,CAACvB,KAAK,CAACC;YAAK;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,eAC/B,CACH,EACAxB,IAAI,CAACvB,KAAK,CAACK,QAAQ,iBAClBzD,OAAA,CAAAE,SAAA;YAAA6F,QAAA,gBACE/F,OAAA;cAAA+F,QAAA,EAAM;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACfnG,OAAA;cAAA+F,QAAA,EAAOpB,IAAI,CAACvB,KAAK,CAACK;YAAQ;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,eAClC,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEjBnG,OAAA,CAACwC,SAAS;QACRC,SAAS,EAAEA,SAAS,CAACkC,IAAI,CAACvB,KAAK,CAAE;QACjCoD,OAAO,EAAEA,CAAA,KAAM9B,OAAO,CAACC,IAAI,CAAE;QAC7B8B,QAAQ,EAAE,CAAChE,SAAS,CAACkC,IAAI,CAACvB,KAAK,CAAE;QAAA2C,QAAA,EAEhCtD,SAAS,CAACkC,IAAI,CAACvB,KAAK,CAAC,GAAG,IAAI,GAAG;MAAM;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA,GAvCCxB,IAAI,CAAC9B,EAAE;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAwCZ,CACX,CAAC;EACJ,CAAC;EAED,oBACEnG,OAAA,CAACG,aAAa;IAAA4F,QAAA,gBACZ/F,OAAA,CAACM,MAAM;MAAAyF,QAAA,gBACL/F,OAAA,CAACQ,KAAK;QAAAuF,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjBnG,OAAA,CAACW,UAAU;QAAC6F,OAAO,EAAEjC,UAAW;QAAAwB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAETnG,OAAA,CAACc,YAAY;MAAAiF,QAAA,gBACX/F,OAAA,CAACgB,GAAG;QACFE,MAAM,EAAEmD,SAAS,KAAK,WAAY;QAClCmC,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC,WAAW,CAAE;QAAAyB,QAAA,EAC1C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnG,OAAA,CAACgB,GAAG;QACFE,MAAM,EAAEmD,SAAS,KAAK,aAAc;QACpCmC,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC,aAAa,CAAE;QAAAyB,QAAA,EAC5C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNnG,OAAA,CAACgB,GAAG;QACFE,MAAM,EAAEmD,SAAS,KAAK,SAAU;QAChCmC,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAAC,SAAS,CAAE;QAAAyB,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEfnG,OAAA,CAACoB,QAAQ;MAAA2E,QAAA,EACNH,WAAW,CAAC;IAAC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEpB,CAAC;AAACjC,EAAA,CAhLID,SAAmB;EAAA,QACKnE,OAAO;AAAA;AAAA4G,IAAA,GAD/BzC,SAAmB;AAkLzB,eAAeA,SAAS;AAAC,IAAA5D,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAgE,IAAA;AAAAC,YAAA,CAAAtG,EAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}