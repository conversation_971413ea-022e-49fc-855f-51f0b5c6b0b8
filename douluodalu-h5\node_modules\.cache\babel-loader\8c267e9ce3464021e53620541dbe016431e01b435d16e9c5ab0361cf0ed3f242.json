{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport GameLogin from './components/GameLogin';\nimport GameMain from './components/GameMain';\nimport { GameProvider } from './context/GameContext';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  width: 100%;\n  height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  overflow: hidden;\n  position: relative;\n`;\n_c = AppContainer;\nconst BackgroundImage = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop');\n  background-size: cover;\n  background-position: center;\n  opacity: 0.3;\n  z-index: 0;\n`;\n_c2 = BackgroundImage;\nfunction App() {\n  _s();\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [user, setUser] = useState(null);\n  useEffect(() => {\n    // 检查本地存储中的用户信息\n    const savedUser = localStorage.getItem('douluoUser');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n      setIsLoggedIn(true);\n    }\n  }, []);\n  const handleLogin = userData => {\n    setUser(userData);\n    setIsLoggedIn(true);\n    localStorage.setItem('douluoUser', JSON.stringify(userData));\n  };\n  const handleLogout = () => {\n    setUser(null);\n    setIsLoggedIn(false);\n    localStorage.removeItem('douluoUser');\n  };\n  return /*#__PURE__*/_jsxDEV(GameProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContainer, {\n      children: [/*#__PURE__*/_jsxDEV(BackgroundImage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), !isLoggedIn ? /*#__PURE__*/_jsxDEV(GameLogin, {\n        onLogin: handleLogin\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(GameMain, {\n        user: user,\n        onLogout: handleLogout\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"Ki3p4Ug0OIeovJYgYQgFusbAAO4=\");\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"BackgroundImage\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "GameLogin", "GameMain", "GameProvider", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "BackgroundImage", "_c2", "App", "_s", "isLoggedIn", "setIsLoggedIn", "user", "setUser", "savedUser", "localStorage", "getItem", "JSON", "parse", "handleLogin", "userData", "setItem", "stringify", "handleLogout", "removeItem", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLogin", "onLogout", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport GameLogin from './components/GameLogin';\nimport GameMain from './components/GameMain';\nimport { GameProvider } from './context/GameContext';\nimport './App.css';\n\nconst AppContainer = styled.div`\n  width: 100%;\n  height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  overflow: hidden;\n  position: relative;\n`;\n\nconst BackgroundImage = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop');\n  background-size: cover;\n  background-position: center;\n  opacity: 0.3;\n  z-index: 0;\n`;\n\nfunction App() {\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [user, setUser] = useState<any>(null);\n\n  useEffect(() => {\n    // 检查本地存储中的用户信息\n    const savedUser = localStorage.getItem('douluoUser');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n      setIsLoggedIn(true);\n    }\n  }, []);\n\n  const handleLogin = (userData: any) => {\n    setUser(userData);\n    setIsLoggedIn(true);\n    localStorage.setItem('douluoUser', JSON.stringify(userData));\n  };\n\n  const handleLogout = () => {\n    setUser(null);\n    setIsLoggedIn(false);\n    localStorage.removeItem('douluoUser');\n  };\n\n  return (\n    <GameProvider>\n      <AppContainer>\n        <BackgroundImage />\n        {!isLoggedIn ? (\n          <GameLogin onLogin={handleLogin} />\n        ) : (\n          <GameMain user={user} onLogout={handleLogout} />\n        )}\n      </AppContainer>\n    </GameProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,YAAY,GAAGN,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,YAAY;AAQlB,MAAMG,eAAe,GAAGT,MAAM,CAACO,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAXID,eAAe;AAarB,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiB,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAM,IAAI,CAAC;EAE3CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMkB,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACpD,IAAIF,SAAS,EAAE;MACbD,OAAO,CAACI,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAC;MAC9BH,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,WAAW,GAAIC,QAAa,IAAK;IACrCP,OAAO,CAACO,QAAQ,CAAC;IACjBT,aAAa,CAAC,IAAI,CAAC;IACnBI,YAAY,CAACM,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACF,QAAQ,CAAC,CAAC;EAC9D,CAAC;EAED,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzBV,OAAO,CAAC,IAAI,CAAC;IACbF,aAAa,CAAC,KAAK,CAAC;IACpBI,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC;EACvC,CAAC;EAED,oBACEtB,OAAA,CAACF,YAAY;IAAAyB,QAAA,eACXvB,OAAA,CAACC,YAAY;MAAAsB,QAAA,gBACXvB,OAAA,CAACI,eAAe;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAClB,CAACnB,UAAU,gBACVR,OAAA,CAACJ,SAAS;QAACgC,OAAO,EAAEX;MAAY;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEnC3B,OAAA,CAACH,QAAQ;QAACa,IAAI,EAAEA,IAAK;QAACmB,QAAQ,EAAER;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAChD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEnB;AAACpB,EAAA,CArCQD,GAAG;AAAAwB,GAAA,GAAHxB,GAAG;AAuCZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAyB,GAAA;AAAAC,YAAA,CAAA5B,EAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}