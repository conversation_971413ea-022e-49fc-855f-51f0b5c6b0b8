{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\components\\\\MainMenu.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MenuContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  position: relative;\n`;\n_c = MenuContainer;\nconst WelcomeSection = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n_c2 = WelcomeSection;\nconst WelcomeTitle = styled.h1`\n  color: #ffd700;\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);\n`;\n_c3 = WelcomeTitle;\nconst WelcomeSubtitle = styled.p`\n  color: #fff;\n  font-size: 1.2rem;\n  margin-bottom: 20px;\n`;\n_c4 = WelcomeSubtitle;\nconst CharacterPreview = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 30px;\n  border: 2px solid #ffd700;\n`;\n_c5 = CharacterPreview;\nconst CharacterInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  margin-bottom: 15px;\n`;\n_c6 = CharacterInfo;\nconst CharacterAvatar = styled.div`\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 40px;\n  border: 3px solid #fff;\n`;\n_c7 = CharacterAvatar;\nconst CharacterDetails = styled.div`\n  flex: 1;\n`;\n_c8 = CharacterDetails;\nconst CharacterName = styled.h3`\n  color: #ffd700;\n  margin: 0 0 10px 0;\n  font-size: 1.5rem;\n`;\n_c9 = CharacterName;\nconst CharacterLevel = styled.div`\n  color: #fff;\n  margin-bottom: 10px;\n`;\n_c0 = CharacterLevel;\nconst ExpBar = styled.div`\n  width: 100%;\n  height: 10px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 5px;\n  overflow: hidden;\n  border: 1px solid #ffd700;\n`;\n_c1 = ExpBar;\nconst ExpFill = styled.div`\n  width: ${props => props.percentage}%;\n  height: 100%;\n  background: linear-gradient(90deg, #4ecdc4, #44a08d);\n  transition: width 0.3s ease;\n`;\n_c10 = ExpFill;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\n  gap: 10px;\n`;\n_c11 = StatsGrid;\nconst StatItem = styled.div`\n  text-align: center;\n  color: #fff;\n`;\n_c12 = StatItem;\nconst StatValue = styled.div`\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #ffd700;\n`;\n_c13 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 0.9rem;\n  opacity: 0.8;\n`;\n_c14 = StatLabel;\nconst MenuGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  flex: 1;\n`;\n_c15 = MenuGrid;\nconst MenuButton = styled.button`\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(26, 26, 46, 0.8));\n  border: 2px solid #ffd700;\n  border-radius: 15px;\n  padding: 30px 20px;\n  color: #fff;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 15px;\n  min-height: 150px;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);\n    border-color: #ffed4e;\n    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(26, 26, 46, 0.9));\n  }\n`;\n_c16 = MenuButton;\nconst MenuIcon = styled.div`\n  font-size: 3rem;\n`;\n_c17 = MenuIcon;\nconst MenuTitle = styled.h3`\n  margin: 0;\n  font-size: 1.3rem;\n  color: #ffd700;\n`;\n_c18 = MenuTitle;\nconst MenuDescription = styled.p`\n  margin: 0;\n  font-size: 0.9rem;\n  opacity: 0.8;\n  text-align: center;\n`;\n_c19 = MenuDescription;\nconst MainMenu = () => {\n  _s();\n  const {\n    state,\n    dispatch\n  } = useGame();\n  const {\n    currentCharacter\n  } = state;\n  const menuItems = [{\n    icon: '⚔️',\n    title: '战斗',\n    description: '挑战魂兽，获得经验和奖励',\n    scene: 'battle'\n  }, {\n    icon: '👤',\n    title: '角色',\n    description: '查看角色信息，装备管理',\n    scene: 'character'\n  }, {\n    icon: '🛒',\n    title: '商店',\n    description: '购买装备和道具',\n    scene: 'shop'\n  }, {\n    icon: '🎲',\n    title: '抽卡',\n    description: '召唤新的魂师伙伴',\n    scene: 'gacha'\n  }, {\n    icon: '🏰',\n    title: '公会',\n    description: '加入公会，团队作战',\n    scene: 'guild'\n  }, {\n    icon: '📋',\n    title: '任务',\n    description: '完成任务获得丰厚奖励',\n    scene: 'quest'\n  }];\n  const handleMenuClick = scene => {\n    dispatch({\n      type: 'SET_SCENE',\n      payload: scene\n    });\n  };\n  const expPercentage = currentCharacter ? currentCharacter.exp / currentCharacter.maxExp * 100 : 0;\n  return /*#__PURE__*/_jsxDEV(MenuContainer, {\n    children: [/*#__PURE__*/_jsxDEV(WelcomeSection, {\n      children: [/*#__PURE__*/_jsxDEV(WelcomeTitle, {\n        children: \"\\u6B22\\u8FCE\\u6765\\u5230\\u6597\\u7F57\\u5927\\u9646\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WelcomeSubtitle, {\n        children: \"\\u6210\\u4E3A\\u6700\\u5F3A\\u9B42\\u5E08\\uFF0C\\u5F81\\u670D\\u9B42\\u517D\\u4E16\\u754C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), currentCharacter && /*#__PURE__*/_jsxDEV(CharacterPreview, {\n      children: [/*#__PURE__*/_jsxDEV(CharacterInfo, {\n        children: [/*#__PURE__*/_jsxDEV(CharacterAvatar, {\n          children: currentCharacter.avatar\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CharacterDetails, {\n          children: [/*#__PURE__*/_jsxDEV(CharacterName, {\n            children: currentCharacter.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CharacterLevel, {\n            children: [\"\\u7B49\\u7EA7 \", currentCharacter.level, \" | \\u9B42\\u529B \", currentCharacter.soulPower]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ExpBar, {\n            children: /*#__PURE__*/_jsxDEV(ExpFill, {\n              percentage: expPercentage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#fff',\n              fontSize: '0.9rem',\n              marginTop: '5px'\n            },\n            children: [\"\\u7ECF\\u9A8C: \", currentCharacter.exp, \"/\", currentCharacter.maxExp]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: currentCharacter.hp\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u751F\\u547D\\u503C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: currentCharacter.mp\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u9B42\\u529B\\u503C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: currentCharacter.attack\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u653B\\u51FB\\u529B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: currentCharacter.defense\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u9632\\u5FA1\\u529B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: currentCharacter.speed\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u901F\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatItem, {\n          children: [/*#__PURE__*/_jsxDEV(StatValue, {\n            children: currentCharacter.soulRings.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"\\u9B42\\u73AF\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(MenuGrid, {\n      children: menuItems.map((item, index) => /*#__PURE__*/_jsxDEV(MenuButton, {\n        onClick: () => handleMenuClick(item.scene),\n        children: [/*#__PURE__*/_jsxDEV(MenuIcon, {\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MenuTitle, {\n          children: item.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MenuDescription, {\n          children: item.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"0Ezd+lYqYM4Ze1hEY5mBTpMQDZc=\", false, function () {\n  return [useGame];\n});\n_c20 = MainMenu;\nexport default MainMenu;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20;\n$RefreshReg$(_c, \"MenuContainer\");\n$RefreshReg$(_c2, \"WelcomeSection\");\n$RefreshReg$(_c3, \"WelcomeTitle\");\n$RefreshReg$(_c4, \"WelcomeSubtitle\");\n$RefreshReg$(_c5, \"CharacterPreview\");\n$RefreshReg$(_c6, \"CharacterInfo\");\n$RefreshReg$(_c7, \"CharacterAvatar\");\n$RefreshReg$(_c8, \"CharacterDetails\");\n$RefreshReg$(_c9, \"CharacterName\");\n$RefreshReg$(_c0, \"CharacterLevel\");\n$RefreshReg$(_c1, \"ExpBar\");\n$RefreshReg$(_c10, \"ExpFill\");\n$RefreshReg$(_c11, \"StatsGrid\");\n$RefreshReg$(_c12, \"StatItem\");\n$RefreshReg$(_c13, \"StatValue\");\n$RefreshReg$(_c14, \"StatLabel\");\n$RefreshReg$(_c15, \"MenuGrid\");\n$RefreshReg$(_c16, \"MenuButton\");\n$RefreshReg$(_c17, \"MenuIcon\");\n$RefreshReg$(_c18, \"MenuTitle\");\n$RefreshReg$(_c19, \"MenuDescription\");\n$RefreshReg$(_c20, \"MainMenu\");", "map": {"version": 3, "names": ["React", "styled", "useGame", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "WelcomeSection", "_c2", "WelcomeTitle", "h1", "_c3", "WelcomeSubtitle", "p", "_c4", "CharacterPreview", "_c5", "CharacterInfo", "_c6", "<PERSON><PERSON><PERSON><PERSON>", "_c7", "CharacterDetails", "_c8", "CharacterName", "h3", "_c9", "CharacterLevel", "_c0", "ExpBar", "_c1", "ExpFill", "props", "percentage", "_c10", "StatsGrid", "_c11", "StatItem", "_c12", "StatValue", "_c13", "StatLabel", "_c14", "MenuGrid", "_c15", "MenuButton", "button", "_c16", "MenuIcon", "_c17", "MenuTitle", "_c18", "MenuDescription", "_c19", "MainMenu", "_s", "state", "dispatch", "currentCharacter", "menuItems", "icon", "title", "description", "scene", "handleMenuClick", "type", "payload", "expPercentage", "exp", "maxExp", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "avatar", "name", "level", "soulPower", "style", "color", "fontSize", "marginTop", "hp", "mp", "attack", "defense", "speed", "soulRings", "length", "map", "item", "index", "onClick", "_c20", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/components/MainMenu.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\n\nconst MenuContainer = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  position: relative;\n`;\n\nconst WelcomeSection = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\nconst WelcomeTitle = styled.h1`\n  color: #ffd700;\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);\n`;\n\nconst WelcomeSubtitle = styled.p`\n  color: #fff;\n  font-size: 1.2rem;\n  margin-bottom: 20px;\n`;\n\nconst CharacterPreview = styled.div`\n  background: rgba(0, 0, 0, 0.6);\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 30px;\n  border: 2px solid #ffd700;\n`;\n\nconst CharacterInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  margin-bottom: 15px;\n`;\n\nconst CharacterAvatar = styled.div`\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 40px;\n  border: 3px solid #fff;\n`;\n\nconst CharacterDetails = styled.div`\n  flex: 1;\n`;\n\nconst CharacterName = styled.h3`\n  color: #ffd700;\n  margin: 0 0 10px 0;\n  font-size: 1.5rem;\n`;\n\nconst CharacterLevel = styled.div`\n  color: #fff;\n  margin-bottom: 10px;\n`;\n\nconst ExpBar = styled.div`\n  width: 100%;\n  height: 10px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 5px;\n  overflow: hidden;\n  border: 1px solid #ffd700;\n`;\n\nconst ExpFill = styled.div<{ percentage: number }>`\n  width: ${props => props.percentage}%;\n  height: 100%;\n  background: linear-gradient(90deg, #4ecdc4, #44a08d);\n  transition: width 0.3s ease;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\n  gap: 10px;\n`;\n\nconst StatItem = styled.div`\n  text-align: center;\n  color: #fff;\n`;\n\nconst StatValue = styled.div`\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #ffd700;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 0.9rem;\n  opacity: 0.8;\n`;\n\nconst MenuGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  flex: 1;\n`;\n\nconst MenuButton = styled.button`\n  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(26, 26, 46, 0.8));\n  border: 2px solid #ffd700;\n  border-radius: 15px;\n  padding: 30px 20px;\n  color: #fff;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 15px;\n  min-height: 150px;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);\n    border-color: #ffed4e;\n    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(26, 26, 46, 0.9));\n  }\n`;\n\nconst MenuIcon = styled.div`\n  font-size: 3rem;\n`;\n\nconst MenuTitle = styled.h3`\n  margin: 0;\n  font-size: 1.3rem;\n  color: #ffd700;\n`;\n\nconst MenuDescription = styled.p`\n  margin: 0;\n  font-size: 0.9rem;\n  opacity: 0.8;\n  text-align: center;\n`;\n\nconst MainMenu: React.FC = () => {\n  const { state, dispatch } = useGame();\n  const { currentCharacter } = state;\n\n  const menuItems = [\n    {\n      icon: '⚔️',\n      title: '战斗',\n      description: '挑战魂兽，获得经验和奖励',\n      scene: 'battle',\n    },\n    {\n      icon: '👤',\n      title: '角色',\n      description: '查看角色信息，装备管理',\n      scene: 'character',\n    },\n    {\n      icon: '🛒',\n      title: '商店',\n      description: '购买装备和道具',\n      scene: 'shop',\n    },\n    {\n      icon: '🎲',\n      title: '抽卡',\n      description: '召唤新的魂师伙伴',\n      scene: 'gacha',\n    },\n    {\n      icon: '🏰',\n      title: '公会',\n      description: '加入公会，团队作战',\n      scene: 'guild',\n    },\n    {\n      icon: '📋',\n      title: '任务',\n      description: '完成任务获得丰厚奖励',\n      scene: 'quest',\n    },\n  ];\n\n  const handleMenuClick = (scene: string) => {\n    dispatch({ type: 'SET_SCENE', payload: scene });\n  };\n\n  const expPercentage = currentCharacter \n    ? (currentCharacter.exp / currentCharacter.maxExp) * 100 \n    : 0;\n\n  return (\n    <MenuContainer>\n      <WelcomeSection>\n        <WelcomeTitle>欢迎来到斗罗大陆</WelcomeTitle>\n        <WelcomeSubtitle>成为最强魂师，征服魂兽世界</WelcomeSubtitle>\n      </WelcomeSection>\n\n      {currentCharacter && (\n        <CharacterPreview>\n          <CharacterInfo>\n            <CharacterAvatar>{currentCharacter.avatar}</CharacterAvatar>\n            <CharacterDetails>\n              <CharacterName>{currentCharacter.name}</CharacterName>\n              <CharacterLevel>等级 {currentCharacter.level} | 魂力 {currentCharacter.soulPower}</CharacterLevel>\n              <ExpBar>\n                <ExpFill percentage={expPercentage} />\n              </ExpBar>\n              <div style={{ color: '#fff', fontSize: '0.9rem', marginTop: '5px' }}>\n                经验: {currentCharacter.exp}/{currentCharacter.maxExp}\n              </div>\n            </CharacterDetails>\n          </CharacterInfo>\n          \n          <StatsGrid>\n            <StatItem>\n              <StatValue>{currentCharacter.hp}</StatValue>\n              <StatLabel>生命值</StatLabel>\n            </StatItem>\n            <StatItem>\n              <StatValue>{currentCharacter.mp}</StatValue>\n              <StatLabel>魂力值</StatLabel>\n            </StatItem>\n            <StatItem>\n              <StatValue>{currentCharacter.attack}</StatValue>\n              <StatLabel>攻击力</StatLabel>\n            </StatItem>\n            <StatItem>\n              <StatValue>{currentCharacter.defense}</StatValue>\n              <StatLabel>防御力</StatLabel>\n            </StatItem>\n            <StatItem>\n              <StatValue>{currentCharacter.speed}</StatValue>\n              <StatLabel>速度</StatLabel>\n            </StatItem>\n            <StatItem>\n              <StatValue>{currentCharacter.soulRings.length}</StatValue>\n              <StatLabel>魂环数</StatLabel>\n            </StatItem>\n          </StatsGrid>\n        </CharacterPreview>\n      )}\n\n      <MenuGrid>\n        {menuItems.map((item, index) => (\n          <MenuButton key={index} onClick={() => handleMenuClick(item.scene)}>\n            <MenuIcon>{item.icon}</MenuIcon>\n            <MenuTitle>{item.title}</MenuTitle>\n            <MenuDescription>{item.description}</MenuDescription>\n          </MenuButton>\n        ))}\n      </MenuGrid>\n    </MenuContainer>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,aAAa,GAAGJ,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,aAAa;AAQnB,MAAMG,cAAc,GAAGP,MAAM,CAACK,GAAG;AACjC;AACA;AACA,CAAC;AAACG,GAAA,GAHID,cAAc;AAKpB,MAAME,YAAY,GAAGT,MAAM,CAACU,EAAE;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,eAAe,GAAGZ,MAAM,CAACa,CAAC;AAChC;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,eAAe;AAMrB,MAAMG,gBAAgB,GAAGf,MAAM,CAACK,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GANID,gBAAgB;AAQtB,MAAME,aAAa,GAAGjB,MAAM,CAACK,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GALID,aAAa;AAOnB,MAAME,eAAe,GAAGnB,MAAM,CAACK,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAVID,eAAe;AAYrB,MAAME,gBAAgB,GAAGrB,MAAM,CAACK,GAAG;AACnC;AACA,CAAC;AAACiB,GAAA,GAFID,gBAAgB;AAItB,MAAME,aAAa,GAAGvB,MAAM,CAACwB,EAAE;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,aAAa;AAMnB,MAAMG,cAAc,GAAG1B,MAAM,CAACK,GAAG;AACjC;AACA;AACA,CAAC;AAACsB,GAAA,GAHID,cAAc;AAKpB,MAAME,MAAM,GAAG5B,MAAM,CAACK,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAPID,MAAM;AASZ,MAAME,OAAO,GAAG9B,MAAM,CAACK,GAA2B;AAClD,WAAW0B,KAAK,IAAIA,KAAK,CAACC,UAAU;AACpC;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIH,OAAO;AAOb,MAAMI,SAAS,GAAGlC,MAAM,CAACK,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GAJID,SAAS;AAMf,MAAME,QAAQ,GAAGpC,MAAM,CAACK,GAAG;AAC3B;AACA;AACA,CAAC;AAACgC,IAAA,GAHID,QAAQ;AAKd,MAAME,SAAS,GAAGtC,MAAM,CAACK,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GAJID,SAAS;AAMf,MAAME,SAAS,GAAGxC,MAAM,CAACK,GAAG;AAC5B;AACA;AACA,CAAC;AAACoC,IAAA,GAHID,SAAS;AAKf,MAAME,QAAQ,GAAG1C,MAAM,CAACK,GAAG;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GALID,QAAQ;AAOd,MAAME,UAAU,GAAG5C,MAAM,CAAC6C,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GApBIF,UAAU;AAsBhB,MAAMG,QAAQ,GAAG/C,MAAM,CAACK,GAAG;AAC3B;AACA,CAAC;AAAC2C,IAAA,GAFID,QAAQ;AAId,MAAME,SAAS,GAAGjD,MAAM,CAACwB,EAAE;AAC3B;AACA;AACA;AACA,CAAC;AAAC0B,IAAA,GAJID,SAAS;AAMf,MAAME,eAAe,GAAGnD,MAAM,CAACa,CAAC;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACuC,IAAA,GALID,eAAe;AAOrB,MAAME,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAGvD,OAAO,CAAC,CAAC;EACrC,MAAM;IAAEwD;EAAiB,CAAC,GAAGF,KAAK;EAElC,MAAMG,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,cAAc;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,SAAS;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,UAAU;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,WAAW,EAAE,YAAY;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAID,KAAa,IAAK;IACzCN,QAAQ,CAAC;MAAEQ,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAEH;IAAM,CAAC,CAAC;EACjD,CAAC;EAED,MAAMI,aAAa,GAAGT,gBAAgB,GACjCA,gBAAgB,CAACU,GAAG,GAAGV,gBAAgB,CAACW,MAAM,GAAI,GAAG,GACtD,CAAC;EAEL,oBACEjE,OAAA,CAACC,aAAa;IAAAiE,QAAA,gBACZlE,OAAA,CAACI,cAAc;MAAA8D,QAAA,gBACblE,OAAA,CAACM,YAAY;QAAA4D,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACrCtE,OAAA,CAACS,eAAe;QAAAyD,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,EAEhBhB,gBAAgB,iBACftD,OAAA,CAACY,gBAAgB;MAAAsD,QAAA,gBACflE,OAAA,CAACc,aAAa;QAAAoD,QAAA,gBACZlE,OAAA,CAACgB,eAAe;UAAAkD,QAAA,EAAEZ,gBAAgB,CAACiB;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,eAC5DtE,OAAA,CAACkB,gBAAgB;UAAAgD,QAAA,gBACflE,OAAA,CAACoB,aAAa;YAAA8C,QAAA,EAAEZ,gBAAgB,CAACkB;UAAI;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC,eACtDtE,OAAA,CAACuB,cAAc;YAAA2C,QAAA,GAAC,eAAG,EAACZ,gBAAgB,CAACmB,KAAK,EAAC,kBAAM,EAACnB,gBAAgB,CAACoB,SAAS;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAC9FtE,OAAA,CAACyB,MAAM;YAAAyC,QAAA,eACLlE,OAAA,CAAC2B,OAAO;cAACE,UAAU,EAAEkC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACTtE,OAAA;YAAK2E,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAM,CAAE;YAAAZ,QAAA,GAAC,gBAC/D,EAACZ,gBAAgB,CAACU,GAAG,EAAC,GAAC,EAACV,gBAAgB,CAACW,MAAM;UAAA;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEhBtE,OAAA,CAAC+B,SAAS;QAAAmC,QAAA,gBACRlE,OAAA,CAACiC,QAAQ;UAAAiC,QAAA,gBACPlE,OAAA,CAACmC,SAAS;YAAA+B,QAAA,EAAEZ,gBAAgB,CAACyB;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5CtE,OAAA,CAACqC,SAAS;YAAA6B,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACXtE,OAAA,CAACiC,QAAQ;UAAAiC,QAAA,gBACPlE,OAAA,CAACmC,SAAS;YAAA+B,QAAA,EAAEZ,gBAAgB,CAAC0B;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5CtE,OAAA,CAACqC,SAAS;YAAA6B,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACXtE,OAAA,CAACiC,QAAQ;UAAAiC,QAAA,gBACPlE,OAAA,CAACmC,SAAS;YAAA+B,QAAA,EAAEZ,gBAAgB,CAAC2B;UAAM;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChDtE,OAAA,CAACqC,SAAS;YAAA6B,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACXtE,OAAA,CAACiC,QAAQ;UAAAiC,QAAA,gBACPlE,OAAA,CAACmC,SAAS;YAAA+B,QAAA,EAAEZ,gBAAgB,CAAC4B;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjDtE,OAAA,CAACqC,SAAS;YAAA6B,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACXtE,OAAA,CAACiC,QAAQ;UAAAiC,QAAA,gBACPlE,OAAA,CAACmC,SAAS;YAAA+B,QAAA,EAAEZ,gBAAgB,CAAC6B;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CtE,OAAA,CAACqC,SAAS;YAAA6B,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACXtE,OAAA,CAACiC,QAAQ;UAAAiC,QAAA,gBACPlE,OAAA,CAACmC,SAAS;YAAA+B,QAAA,EAAEZ,gBAAgB,CAAC8B,SAAS,CAACC;UAAM;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1DtE,OAAA,CAACqC,SAAS;YAAA6B,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACnB,eAEDtE,OAAA,CAACuC,QAAQ;MAAA2B,QAAA,EACNX,SAAS,CAAC+B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBxF,OAAA,CAACyC,UAAU;QAAagD,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAAC2B,IAAI,CAAC5B,KAAK,CAAE;QAAAO,QAAA,gBACjElE,OAAA,CAAC4C,QAAQ;UAAAsB,QAAA,EAAEqB,IAAI,CAAC/B;QAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAChCtE,OAAA,CAAC8C,SAAS;UAAAoB,QAAA,EAAEqB,IAAI,CAAC9B;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACnCtE,OAAA,CAACgD,eAAe;UAAAkB,QAAA,EAAEqB,IAAI,CAAC7B;QAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC;MAAA,GAHtCkB,KAAK;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIV,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEpB,CAAC;AAACnB,EAAA,CAlHID,QAAkB;EAAA,QACMpD,OAAO;AAAA;AAAA4F,IAAA,GAD/BxC,QAAkB;AAoHxB,eAAeA,QAAQ;AAAC,IAAA/C,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAyC,IAAA;AAAAC,YAAA,CAAAxF,EAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAA5C,IAAA;AAAA4C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}