{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\u684C\\u9762\\\\\\u8F6F\\u4EF6\\\\douluodalu-h5\\\\src\\\\components\\\\GameMain.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\nimport TopBar from './TopBar';\nimport MainMenu from './MainMenu';\nimport CharacterPanel from './CharacterPanel';\nimport BattleScene from './BattleScene';\nimport ShopPanel from './ShopPanel';\nimport GachaPanel from './GachaPanel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameContainer = styled.div`\n  width: 100%;\n  height: 100vh;\n  position: relative;\n  z-index: 10;\n  display: flex;\n  flex-direction: column;\n`;\n_c = GameContainer;\nconst ContentArea = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n`;\n_c2 = ContentArea;\nconst GameMain = ({\n  user,\n  onLogout\n}) => {\n  _s();\n  const {\n    state,\n    dispatch\n  } = useGame();\n  useEffect(() => {\n    // 初始化用户数据\n    dispatch({\n      type: 'SET_USER',\n      payload: user\n    });\n\n    // 创建初始角色（如果没有的话）\n    if (state.characters.length === 0) {\n      const initialCharacter = {\n        id: 'char_1',\n        name: '唐三',\n        level: 1,\n        exp: 0,\n        maxExp: 100,\n        hp: 100,\n        maxHp: 100,\n        mp: 50,\n        maxMp: 50,\n        attack: 25,\n        defense: 15,\n        speed: 20,\n        soulPower: 1,\n        soulRings: [],\n        equipment: [],\n        avatar: '🧙‍♂️'\n      };\n      dispatch({\n        type: 'ADD_CHARACTER',\n        payload: initialCharacter\n      });\n      dispatch({\n        type: 'SET_CURRENT_CHARACTER',\n        payload: initialCharacter\n      });\n    }\n  }, [user, dispatch, state.characters.length]);\n  const renderCurrentScene = () => {\n    switch (state.currentScene) {\n      case 'character':\n        return /*#__PURE__*/_jsxDEV(CharacterPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 16\n        }, this);\n      case 'battle':\n        return /*#__PURE__*/_jsxDEV(BattleScene, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 16\n        }, this);\n      case 'shop':\n        return /*#__PURE__*/_jsxDEV(ShopPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 16\n        }, this);\n      case 'gacha':\n        return /*#__PURE__*/_jsxDEV(GachaPanel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(MainMenu, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(GameContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TopBar, {\n      user: state.user,\n      onLogout: onLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n      children: renderCurrentScene()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(GameMain, \"SMlZY2uyVTClKrSyPFthlqGR0nA=\", false, function () {\n  return [useGame];\n});\n_c3 = GameMain;\nexport default GameMain;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"GameContainer\");\n$RefreshReg$(_c2, \"ContentArea\");\n$RefreshReg$(_c3, \"GameMain\");", "map": {"version": 3, "names": ["React", "useEffect", "styled", "useGame", "TopBar", "MainMenu", "CharacterPanel", "BattleScene", "ShopPanel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "GameContainer", "div", "_c", "ContentArea", "_c2", "GameMain", "user", "onLogout", "_s", "state", "dispatch", "type", "payload", "characters", "length", "initialCharacter", "id", "name", "level", "exp", "maxExp", "hp", "maxHp", "mp", "maxMp", "attack", "defense", "speed", "soulPower", "soulRings", "equipment", "avatar", "renderCurrentScene", "currentScene", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/src/components/GameMain.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useGame } from '../context/GameContext';\nimport TopBar from './TopBar';\nimport MainMenu from './MainMenu';\nimport CharacterPanel from './CharacterPanel';\nimport BattleScene from './BattleScene';\nimport ShopPanel from './ShopPanel';\nimport GachaPanel from './GachaPanel';\n\nconst GameContainer = styled.div`\n  width: 100%;\n  height: 100vh;\n  position: relative;\n  z-index: 10;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n`;\n\ninterface GameMainProps {\n  user: any;\n  onLogout: () => void;\n}\n\nconst GameMain: React.FC<GameMainProps> = ({ user, onLogout }) => {\n  const { state, dispatch } = useGame();\n\n  useEffect(() => {\n    // 初始化用户数据\n    dispatch({ type: 'SET_USER', payload: user });\n    \n    // 创建初始角色（如果没有的话）\n    if (state.characters.length === 0) {\n      const initialCharacter = {\n        id: 'char_1',\n        name: '唐三',\n        level: 1,\n        exp: 0,\n        maxExp: 100,\n        hp: 100,\n        maxHp: 100,\n        mp: 50,\n        maxMp: 50,\n        attack: 25,\n        defense: 15,\n        speed: 20,\n        soulPower: 1,\n        soulRings: [],\n        equipment: [],\n        avatar: '🧙‍♂️',\n      };\n      \n      dispatch({ type: 'ADD_CHARACTER', payload: initialCharacter });\n      dispatch({ type: 'SET_CURRENT_CHARACTER', payload: initialCharacter });\n    }\n  }, [user, dispatch, state.characters.length]);\n\n  const renderCurrentScene = () => {\n    switch (state.currentScene) {\n      case 'character':\n        return <CharacterPanel />;\n      case 'battle':\n        return <BattleScene />;\n      case 'shop':\n        return <ShopPanel />;\n      case 'gacha':\n        return <GachaPanel />;\n      default:\n        return <MainMenu />;\n    }\n  };\n\n  return (\n    <GameContainer>\n      <TopBar user={state.user} onLogout={onLogout} />\n      <ContentArea>\n        {renderCurrentScene()}\n      </ContentArea>\n    </GameContainer>\n  );\n};\n\nexport default GameMain;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,aAAa,GAAGV,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,aAAa;AASnB,MAAMG,WAAW,GAAGb,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,WAAW;AAYjB,MAAME,QAAiC,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAErCF,SAAS,CAAC,MAAM;IACd;IACAqB,QAAQ,CAAC;MAAEC,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAEN;IAAK,CAAC,CAAC;;IAE7C;IACA,IAAIG,KAAK,CAACI,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACjC,MAAMC,gBAAgB,GAAG;QACvBC,EAAE,EAAE,QAAQ;QACZC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,CAAC;QACRC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE,GAAG;QACXC,EAAE,EAAE,GAAG;QACPC,KAAK,EAAE,GAAG;QACVC,EAAE,EAAE,EAAE;QACNC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE;MACV,CAAC;MAEDrB,QAAQ,CAAC;QAAEC,IAAI,EAAE,eAAe;QAAEC,OAAO,EAAEG;MAAiB,CAAC,CAAC;MAC9DL,QAAQ,CAAC;QAAEC,IAAI,EAAE,uBAAuB;QAAEC,OAAO,EAAEG;MAAiB,CAAC,CAAC;IACxE;EACF,CAAC,EAAE,CAACT,IAAI,EAAEI,QAAQ,EAAED,KAAK,CAACI,UAAU,CAACC,MAAM,CAAC,CAAC;EAE7C,MAAMkB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQvB,KAAK,CAACwB,YAAY;MACxB,KAAK,WAAW;QACd,oBAAOlC,OAAA,CAACL,cAAc;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3B,KAAK,QAAQ;QACX,oBAAOtC,OAAA,CAACJ,WAAW;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxB,KAAK,MAAM;QACT,oBAAOtC,OAAA,CAACH,SAAS;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,OAAO;QACV,oBAAOtC,OAAA,CAACF,UAAU;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvB;QACE,oBAAOtC,OAAA,CAACN,QAAQ;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACvB;EACF,CAAC;EAED,oBACEtC,OAAA,CAACC,aAAa;IAAAsC,QAAA,gBACZvC,OAAA,CAACP,MAAM;MAACc,IAAI,EAAEG,KAAK,CAACH,IAAK;MAACC,QAAQ,EAAEA;IAAS;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChDtC,OAAA,CAACI,WAAW;MAAAmC,QAAA,EACTN,kBAAkB,CAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB,CAAC;AAAC7B,EAAA,CAxDIH,QAAiC;EAAA,QACTd,OAAO;AAAA;AAAAgD,GAAA,GAD/BlC,QAAiC;AA0DvC,eAAeA,QAAQ;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAmC,GAAA;AAAAC,YAAA,CAAAtC,EAAA;AAAAsC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}