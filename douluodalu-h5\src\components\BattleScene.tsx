import React, { useState } from 'react';
import styled from 'styled-components';
import { useGame } from '../context/GameContext';

const BattleContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const Title = styled.h2`
  color: #ffd700;
  margin: 0;
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid #ffd700;
  color: #ffd700;
  padding: 10px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 215, 0, 0.2);
  }
`;

const BattleArea = styled.div`
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 20px;
`;

const CharacterSide = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const CharacterCard = styled.div`
  background: rgba(0, 0, 0, 0.6);
  border-radius: 15px;
  padding: 20px;
  border: 2px solid #ffd700;
  width: 100%;
  max-width: 300px;
`;

const CharacterAvatar = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50px;
  border: 3px solid #fff;
  margin: 0 auto 15px;
`;

const CharacterName = styled.h3`
  color: #ffd700;
  text-align: center;
  margin-bottom: 15px;
`;

const HealthBar = styled.div`
  width: 100%;
  height: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid #e74c3c;
  margin-bottom: 10px;
`;

const HealthFill = styled.div<{ percentage: number }>`
  width: ${props => props.percentage}%;
  height: 100%;
  background: linear-gradient(90deg, #e74c3c, #c0392b);
  transition: width 0.5s ease;
`;

const HealthText = styled.div`
  text-align: center;
  color: #fff;
  font-size: 14px;
  margin-bottom: 15px;
`;

const Stats = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  font-size: 14px;
`;

const StatItem = styled.div`
  color: #fff;
  text-align: center;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
`;

const ActionButton = styled.button<{ variant?: 'attack' | 'skill' | 'defend' }>`
  padding: 15px 25px;
  border: none;
  border-radius: 10px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => {
    switch (props.variant) {
      case 'attack':
        return `
          background: linear-gradient(45deg, #e74c3c, #c0392b);
          color: white;
          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4); }
        `;
      case 'skill':
        return `
          background: linear-gradient(45deg, #9b59b6, #8e44ad);
          color: white;
          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4); }
        `;
      case 'defend':
        return `
          background: linear-gradient(45deg, #3498db, #2980b9);
          color: white;
          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4); }
        `;
      default:
        return `
          background: linear-gradient(45deg, #ffd700, #ffed4e);
          color: black;
          &:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4); }
        `;
    }
  }}
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const BattleLog = styled.div`
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 15px;
  height: 150px;
  overflow-y: auto;
  border: 2px solid #ffd700;
`;

const LogEntry = styled.div`
  color: #fff;
  margin-bottom: 5px;
  font-size: 14px;
  
  &.damage {
    color: #e74c3c;
  }
  
  &.heal {
    color: #2ecc71;
  }
  
  &.info {
    color: #3498db;
  }
`;

const EnemySelection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
`;

const EnemyCard = styled.div`
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid #666;
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  
  &:hover {
    border-color: #ffd700;
    transform: translateY(-3px);
  }
`;

const enemies = [
  { id: 1, name: '森林狼', level: 1, hp: 80, maxHp: 80, attack: 15, defense: 8, avatar: '🐺' },
  { id: 2, name: '岩石熊', level: 2, hp: 120, maxHp: 120, attack: 20, defense: 15, avatar: '🐻' },
  { id: 3, name: '火焰鸟', level: 3, hp: 100, maxHp: 100, attack: 25, defense: 10, avatar: '🔥🦅' },
  { id: 4, name: '冰霜虎', level: 4, hp: 150, maxHp: 150, attack: 30, defense: 20, avatar: '🐅' },
];

const BattleScene: React.FC = () => {
  const { state, dispatch } = useGame();
  const { currentCharacter, battleState } = state;
  const [selectedEnemy, setSelectedEnemy] = useState<any>(null);
  const [isPlayerTurn, setIsPlayerTurn] = useState(true);
  const [battleInProgress, setBattleInProgress] = useState(false);

  const handleBack = () => {
    dispatch({ type: 'SET_SCENE', payload: 'main' });
    if (battleState.isInBattle) {
      dispatch({ type: 'END_BATTLE' });
    }
  };

  const startBattle = (enemy: any) => {
    setSelectedEnemy({ ...enemy });
    setBattleInProgress(true);
    setIsPlayerTurn(true);
    dispatch({ type: 'START_BATTLE', payload: enemy });
    dispatch({ type: 'ADD_BATTLE_LOG', payload: `战斗开始！你遇到了 ${enemy.name}` });
  };

  const calculateDamage = (attacker: any, defender: any, isSkill = false) => {
    const baseDamage = attacker.attack;
    const defense = defender.defense;
    const skillMultiplier = isSkill ? 1.5 : 1;
    const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2倍随机
    
    const damage = Math.max(1, Math.floor((baseDamage * skillMultiplier - defense * 0.5) * randomFactor));
    return damage;
  };

  const playerAttack = (isSkill = false) => {
    if (!currentCharacter || !selectedEnemy || !isPlayerTurn) return;

    const damage = calculateDamage(currentCharacter, selectedEnemy, isSkill);
    const newEnemyHp = Math.max(0, selectedEnemy.hp - damage);
    
    setSelectedEnemy((prev: any) => ({ ...prev, hp: newEnemyHp }));
    
    const actionText = isSkill ? '使用技能攻击' : '普通攻击';
    dispatch({ type: 'ADD_BATTLE_LOG', payload: `${currentCharacter.name} ${actionText} ${selectedEnemy.name}，造成 ${damage} 点伤害` });

    if (newEnemyHp <= 0) {
      // 敌人死亡
      dispatch({ type: 'ADD_BATTLE_LOG', payload: `${selectedEnemy.name} 被击败了！` });
      
      // 获得经验和奖励
      const expGain = selectedEnemy.level * 20;
      const coinGain = selectedEnemy.level * 10;
      
      const updatedCharacter = {
        ...currentCharacter,
        exp: currentCharacter.exp + expGain,
      };
      
      // 检查升级
      if (updatedCharacter.exp >= updatedCharacter.maxExp) {
        updatedCharacter.level += 1;
        updatedCharacter.exp = updatedCharacter.exp - updatedCharacter.maxExp;
        updatedCharacter.maxExp = Math.floor(updatedCharacter.maxExp * 1.2);
        updatedCharacter.maxHp += 20;
        updatedCharacter.hp = updatedCharacter.maxHp;
        updatedCharacter.attack += 5;
        updatedCharacter.defense += 3;
        
        dispatch({ type: 'ADD_BATTLE_LOG', payload: `${currentCharacter.name} 升级了！等级提升到 ${updatedCharacter.level}` });
      }
      
      dispatch({ type: 'UPDATE_CHARACTER', payload: updatedCharacter });
      dispatch({ type: 'UPDATE_CURRENCY', payload: { coins: coinGain } });
      dispatch({ type: 'ADD_BATTLE_LOG', payload: `获得 ${expGain} 经验值和 ${coinGain} 金币` });
      
      // 结束战斗
      setTimeout(() => {
        setBattleInProgress(false);
        setSelectedEnemy(null);
        dispatch({ type: 'END_BATTLE' });
      }, 2000);
      
      return;
    }

    setIsPlayerTurn(false);
    
    // 敌人回合
    setTimeout(() => {
      enemyAttack();
    }, 1000);
  };

  const enemyAttack = () => {
    if (!currentCharacter || !selectedEnemy) return;

    const damage = calculateDamage(selectedEnemy, currentCharacter);
    const newPlayerHp = Math.max(0, currentCharacter.hp - damage);
    
    const updatedCharacter = { ...currentCharacter, hp: newPlayerHp };
    dispatch({ type: 'UPDATE_CHARACTER', payload: updatedCharacter });
    dispatch({ type: 'ADD_BATTLE_LOG', payload: `${selectedEnemy.name} 攻击 ${currentCharacter.name}，造成 ${damage} 点伤害` });

    if (newPlayerHp <= 0) {
      dispatch({ type: 'ADD_BATTLE_LOG', payload: `${currentCharacter.name} 被击败了...` });
      setTimeout(() => {
        setBattleInProgress(false);
        setSelectedEnemy(null);
        dispatch({ type: 'END_BATTLE' });
        // 复活角色
        const revivedCharacter = { ...updatedCharacter, hp: Math.floor(updatedCharacter.maxHp * 0.5) };
        dispatch({ type: 'UPDATE_CHARACTER', payload: revivedCharacter });
      }, 2000);
      return;
    }

    setIsPlayerTurn(true);
  };

  const playerDefend = () => {
    if (!currentCharacter || !isPlayerTurn) return;
    
    dispatch({ type: 'ADD_BATTLE_LOG', payload: `${currentCharacter.name} 进入防御状态` });
    setIsPlayerTurn(false);
    
    setTimeout(() => {
      enemyAttack();
    }, 1000);
  };

  if (!currentCharacter) {
    return (
      <BattleContainer>
        <Header>
          <Title>战斗</Title>
          <BackButton onClick={handleBack}>返回</BackButton>
        </Header>
        <div style={{ color: '#fff', textAlign: 'center' }}>请先选择角色</div>
      </BattleContainer>
    );
  }

  return (
    <BattleContainer>
      <Header>
        <Title>战斗场景</Title>
        <BackButton onClick={handleBack}>返回</BackButton>
      </Header>

      {!battleInProgress ? (
        <>
          <h3 style={{ color: '#ffd700', marginBottom: '20px' }}>选择对手</h3>
          <EnemySelection>
            {enemies.map(enemy => (
              <EnemyCard key={enemy.id} onClick={() => startBattle(enemy)}>
                <div style={{ fontSize: '40px', marginBottom: '10px' }}>{enemy.avatar}</div>
                <div style={{ color: '#ffd700', fontWeight: 'bold', marginBottom: '5px' }}>
                  {enemy.name}
                </div>
                <div style={{ color: '#fff', fontSize: '14px' }}>
                  等级 {enemy.level} | 生命值 {enemy.hp}
                </div>
                <div style={{ color: '#fff', fontSize: '12px' }}>
                  攻击 {enemy.attack} | 防御 {enemy.defense}
                </div>
              </EnemyCard>
            ))}
          </EnemySelection>
        </>
      ) : (
        <>
          <BattleArea>
            <CharacterSide>
              <h3 style={{ color: '#ffd700', marginBottom: '15px' }}>我方</h3>
              <CharacterCard>
                <CharacterAvatar>{currentCharacter.avatar}</CharacterAvatar>
                <CharacterName>{currentCharacter.name}</CharacterName>
                <HealthBar>
                  <HealthFill percentage={(currentCharacter.hp / currentCharacter.maxHp) * 100} />
                </HealthBar>
                <HealthText>{currentCharacter.hp}/{currentCharacter.maxHp}</HealthText>
                <Stats>
                  <StatItem>攻击: {currentCharacter.attack}</StatItem>
                  <StatItem>防御: {currentCharacter.defense}</StatItem>
                  <StatItem>速度: {currentCharacter.speed}</StatItem>
                  <StatItem>等级: {currentCharacter.level}</StatItem>
                </Stats>
              </CharacterCard>
            </CharacterSide>

            <CharacterSide>
              <h3 style={{ color: '#e74c3c', marginBottom: '15px' }}>敌方</h3>
              <CharacterCard style={{ borderColor: '#e74c3c' }}>
                <CharacterAvatar style={{ background: 'linear-gradient(45deg, #e74c3c, #c0392b)' }}>
                  {selectedEnemy?.avatar}
                </CharacterAvatar>
                <CharacterName style={{ color: '#e74c3c' }}>{selectedEnemy?.name}</CharacterName>
                <HealthBar>
                  <HealthFill percentage={(selectedEnemy?.hp / selectedEnemy?.maxHp) * 100} />
                </HealthBar>
                <HealthText>{selectedEnemy?.hp}/{selectedEnemy?.maxHp}</HealthText>
                <Stats>
                  <StatItem>攻击: {selectedEnemy?.attack}</StatItem>
                  <StatItem>防御: {selectedEnemy?.defense}</StatItem>
                  <StatItem>等级: {selectedEnemy?.level}</StatItem>
                  <StatItem>状态: {selectedEnemy?.hp > 0 ? '存活' : '死亡'}</StatItem>
                </Stats>
              </CharacterCard>
            </CharacterSide>
          </BattleArea>

          <ActionButtons>
            <ActionButton 
              variant="attack" 
              onClick={() => playerAttack(false)}
              disabled={!isPlayerTurn || selectedEnemy?.hp <= 0}
            >
              普通攻击
            </ActionButton>
            <ActionButton 
              variant="skill" 
              onClick={() => playerAttack(true)}
              disabled={!isPlayerTurn || selectedEnemy?.hp <= 0 || currentCharacter.mp < 10}
            >
              技能攻击
            </ActionButton>
            <ActionButton 
              variant="defend" 
              onClick={playerDefend}
              disabled={!isPlayerTurn || selectedEnemy?.hp <= 0}
            >
              防御
            </ActionButton>
          </ActionButtons>
        </>
      )}

      <BattleLog>
        {battleState.battleLog.map((log, index) => (
          <LogEntry 
            key={index} 
            className={log.includes('伤害') ? 'damage' : log.includes('获得') ? 'heal' : 'info'}
          >
            {log}
          </LogEntry>
        ))}
      </BattleLog>
    </BattleContainer>
  );
};

export default BattleScene;
