{"ast": null, "code": "/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\n\"production\" !== process.env.NODE_ENV && function () {\n  function getComponentNameFromType(type) {\n    if (null == type) return null;\n    if (\"function\" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;\n    if (\"string\" === typeof type) return type;\n    switch (type) {\n      case REACT_FRAGMENT_TYPE:\n        return \"Fragment\";\n      case REACT_PROFILER_TYPE:\n        return \"Profiler\";\n      case REACT_STRICT_MODE_TYPE:\n        return \"StrictMode\";\n      case REACT_SUSPENSE_TYPE:\n        return \"Suspense\";\n      case REACT_SUSPENSE_LIST_TYPE:\n        return \"SuspenseList\";\n      case REACT_ACTIVITY_TYPE:\n        return \"Activity\";\n    }\n    if (\"object\" === typeof type) switch (\"number\" === typeof type.tag && console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"), type.$$typeof) {\n      case REACT_PORTAL_TYPE:\n        return \"Portal\";\n      case REACT_CONTEXT_TYPE:\n        return (type.displayName || \"Context\") + \".Provider\";\n      case REACT_CONSUMER_TYPE:\n        return (type._context.displayName || \"Context\") + \".Consumer\";\n      case REACT_FORWARD_REF_TYPE:\n        var innerType = type.render;\n        type = type.displayName;\n        type || (type = innerType.displayName || innerType.name || \"\", type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\");\n        return type;\n      case REACT_MEMO_TYPE:\n        return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || \"Memo\";\n      case REACT_LAZY_TYPE:\n        innerType = type._payload;\n        type = type._init;\n        try {\n          return getComponentNameFromType(type(innerType));\n        } catch (x) {}\n    }\n    return null;\n  }\n  function testStringCoercion(value) {\n    return \"\" + value;\n  }\n  function checkKeyStringCoercion(value) {\n    try {\n      testStringCoercion(value);\n      var JSCompiler_inline_result = !1;\n    } catch (e) {\n      JSCompiler_inline_result = !0;\n    }\n    if (JSCompiler_inline_result) {\n      JSCompiler_inline_result = console;\n      var JSCompiler_temp_const = JSCompiler_inline_result.error;\n      var JSCompiler_inline_result$jscomp$0 = \"function\" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || \"Object\";\n      JSCompiler_temp_const.call(JSCompiler_inline_result, \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\", JSCompiler_inline_result$jscomp$0);\n      return testStringCoercion(value);\n    }\n  }\n  function getTaskName(type) {\n    if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n    if (\"object\" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return \"<...>\";\n    try {\n      var name = getComponentNameFromType(type);\n      return name ? \"<\" + name + \">\" : \"<...>\";\n    } catch (x) {\n      return \"<...>\";\n    }\n  }\n  function getOwner() {\n    var dispatcher = ReactSharedInternals.A;\n    return null === dispatcher ? null : dispatcher.getOwner();\n  }\n  function UnknownOwner() {\n    return Error(\"react-stack-top-frame\");\n  }\n  function hasValidKey(config) {\n    if (hasOwnProperty.call(config, \"key\")) {\n      var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n      if (getter && getter.isReactWarning) return !1;\n    }\n    return void 0 !== config.key;\n  }\n  function defineKeyPropWarningGetter(props, displayName) {\n    function warnAboutAccessingKey() {\n      specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\", displayName));\n    }\n    warnAboutAccessingKey.isReactWarning = !0;\n    Object.defineProperty(props, \"key\", {\n      get: warnAboutAccessingKey,\n      configurable: !0\n    });\n  }\n  function elementRefGetterWithDeprecationWarning() {\n    var componentName = getComponentNameFromType(this.type);\n    didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"));\n    componentName = this.props.ref;\n    return void 0 !== componentName ? componentName : null;\n  }\n  function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {\n    self = props.ref;\n    type = {\n      $$typeof: REACT_ELEMENT_TYPE,\n      type: type,\n      key: key,\n      props: props,\n      _owner: owner\n    };\n    null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, \"ref\", {\n      enumerable: !1,\n      get: elementRefGetterWithDeprecationWarning\n    }) : Object.defineProperty(type, \"ref\", {\n      enumerable: !1,\n      value: null\n    });\n    type._store = {};\n    Object.defineProperty(type._store, \"validated\", {\n      configurable: !1,\n      enumerable: !1,\n      writable: !0,\n      value: 0\n    });\n    Object.defineProperty(type, \"_debugInfo\", {\n      configurable: !1,\n      enumerable: !1,\n      writable: !0,\n      value: null\n    });\n    Object.defineProperty(type, \"_debugStack\", {\n      configurable: !1,\n      enumerable: !1,\n      writable: !0,\n      value: debugStack\n    });\n    Object.defineProperty(type, \"_debugTask\", {\n      configurable: !1,\n      enumerable: !1,\n      writable: !0,\n      value: debugTask\n    });\n    Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n    return type;\n  }\n  function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {\n    var children = config.children;\n    if (void 0 !== children) if (isStaticChildren) {\n      if (isArrayImpl(children)) {\n        for (isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++) validateChildKeys(children[isStaticChildren]);\n        Object.freeze && Object.freeze(children);\n      } else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");\n    } else validateChildKeys(children);\n    if (hasOwnProperty.call(config, \"key\")) {\n      children = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return \"key\" !== k;\n      });\n      isStaticChildren = 0 < keys.length ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\" : \"{key: someKey}\";\n      didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\", console.error('A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);\n    }\n    children = null;\n    void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = \"\" + maybeKey);\n    hasValidKey(config) && (checkKeyStringCoercion(config.key), children = \"\" + config.key);\n    if (\"key\" in config) {\n      maybeKey = {};\n      for (var propName in config) \"key\" !== propName && (maybeKey[propName] = config[propName]);\n    } else maybeKey = config;\n    children && defineKeyPropWarningGetter(maybeKey, \"function\" === typeof type ? type.displayName || type.name || \"Unknown\" : type);\n    return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);\n  }\n  function validateChildKeys(node) {\n    \"object\" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);\n  }\n  var React = require(\"react\"),\n    REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n    REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n    REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n    REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n    REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n  Symbol.for(\"react.provider\");\n  var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n    REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n    REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n    REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n    REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n    REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n    REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n    REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n    REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n    ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n    hasOwnProperty = Object.prototype.hasOwnProperty,\n    isArrayImpl = Array.isArray,\n    createTask = console.createTask ? console.createTask : function () {\n      return null;\n    };\n  React = {\n    \"react-stack-bottom-frame\": function (callStackForError) {\n      return callStackForError();\n    }\n  };\n  var specialPropKeyWarningShown;\n  var didWarnAboutElementRef = {};\n  var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(React, UnknownOwner)();\n  var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n  var didWarnAboutKeySpread = {};\n  exports.Fragment = REACT_FRAGMENT_TYPE;\n  exports.jsxDEV = function (type, config, maybeKey, isStaticChildren, source, self) {\n    var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n    return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error(\"react-stack-top-frame\") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);\n  };\n}();", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "getComponentNameFromType", "type", "$$typeof", "REACT_CLIENT_REFERENCE", "displayName", "name", "REACT_FRAGMENT_TYPE", "REACT_PROFILER_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_ACTIVITY_TYPE", "tag", "console", "error", "REACT_PORTAL_TYPE", "REACT_CONTEXT_TYPE", "REACT_CONSUMER_TYPE", "_context", "REACT_FORWARD_REF_TYPE", "innerType", "render", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "_payload", "_init", "x", "testStringCoercion", "value", "checkKeyStringCoercion", "JSCompiler_inline_result", "e", "JSCompiler_temp_const", "JSCompiler_inline_result$jscomp$0", "Symbol", "toStringTag", "constructor", "call", "getTaskName", "get<PERSON>wner", "dispatcher", "ReactSharedInternals", "A", "<PERSON><PERSON><PERSON><PERSON>", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "hasOwnProperty", "getter", "Object", "getOwnPropertyDescriptor", "get", "isReactWarning", "key", "defineKeyPropWarningGetter", "props", "warnAboutAccessingKey", "specialPropKeyWarningShown", "defineProperty", "configurable", "elementRefGetterWithDeprecationWarning", "componentName", "didWarnAboutElementRef", "ref", "ReactElement", "self", "source", "owner", "debugStack", "debugTask", "REACT_ELEMENT_TYPE", "_owner", "enumerable", "_store", "writable", "freeze", "jsxDEVImpl", "<PERSON><PERSON><PERSON>", "isStaticChildren", "children", "isArrayImpl", "length", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "filter", "k", "join", "didWarnAboutKeySpread", "propName", "node", "validated", "React", "require", "for", "__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "prototype", "Array", "isArray", "createTask", "react-stack-bottom-frame", "callStackForError", "unknownOwnerDebugStack", "bind", "unknownOwnerDebugTask", "exports", "Fragment", "jsxDEV", "trackActualOwner", "recentlyCreatedOwnerStacks"], "sources": ["C:/Users/<USER>/OneDrive/桌面/软件/douluodalu-h5/node_modules/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,YAAY,KAAKA,OAAO,CAACC,GAAG,CAACC,QAAQ,IAClC,YAAY;EACX,SAASC,wBAAwBA,CAACC,IAAI,EAAE;IACtC,IAAI,IAAI,IAAIA,IAAI,EAAE,OAAO,IAAI;IAC7B,IAAI,UAAU,KAAK,OAAOA,IAAI,EAC5B,OAAOA,IAAI,CAACC,QAAQ,KAAKC,sBAAsB,GAC3C,IAAI,GACJF,IAAI,CAACG,WAAW,IAAIH,IAAI,CAACI,IAAI,IAAI,IAAI;IAC3C,IAAI,QAAQ,KAAK,OAAOJ,IAAI,EAAE,OAAOA,IAAI;IACzC,QAAQA,IAAI;MACV,KAAKK,mBAAmB;QACtB,OAAO,UAAU;MACnB,KAAKC,mBAAmB;QACtB,OAAO,UAAU;MACnB,KAAKC,sBAAsB;QACzB,OAAO,YAAY;MACrB,KAAKC,mBAAmB;QACtB,OAAO,UAAU;MACnB,KAAKC,wBAAwB;QAC3B,OAAO,cAAc;MACvB,KAAKC,mBAAmB;QACtB,OAAO,UAAU;IACrB;IACA,IAAI,QAAQ,KAAK,OAAOV,IAAI,EAC1B,QACG,QAAQ,KAAK,OAAOA,IAAI,CAACW,GAAG,IAC3BC,OAAO,CAACC,KAAK,CACX,mHACF,CAAC,EACHb,IAAI,CAACC,QAAQ;MAEb,KAAKa,iBAAiB;QACpB,OAAO,QAAQ;MACjB,KAAKC,kBAAkB;QACrB,OAAO,CAACf,IAAI,CAACG,WAAW,IAAI,SAAS,IAAI,WAAW;MACtD,KAAKa,mBAAmB;QACtB,OAAO,CAAChB,IAAI,CAACiB,QAAQ,CAACd,WAAW,IAAI,SAAS,IAAI,WAAW;MAC/D,KAAKe,sBAAsB;QACzB,IAAIC,SAAS,GAAGnB,IAAI,CAACoB,MAAM;QAC3BpB,IAAI,GAAGA,IAAI,CAACG,WAAW;QACvBH,IAAI,KACAA,IAAI,GAAGmB,SAAS,CAAChB,WAAW,IAAIgB,SAAS,CAACf,IAAI,IAAI,EAAE,EACrDJ,IAAI,GAAG,EAAE,KAAKA,IAAI,GAAG,aAAa,GAAGA,IAAI,GAAG,GAAG,GAAG,YAAa,CAAC;QACnE,OAAOA,IAAI;MACb,KAAKqB,eAAe;QAClB,OACGF,SAAS,GAAGnB,IAAI,CAACG,WAAW,IAAI,IAAI,EACrC,IAAI,KAAKgB,SAAS,GACdA,SAAS,GACTpB,wBAAwB,CAACC,IAAI,CAACA,IAAI,CAAC,IAAI,MAAM;MAErD,KAAKsB,eAAe;QAClBH,SAAS,GAAGnB,IAAI,CAACuB,QAAQ;QACzBvB,IAAI,GAAGA,IAAI,CAACwB,KAAK;QACjB,IAAI;UACF,OAAOzB,wBAAwB,CAACC,IAAI,CAACmB,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,OAAOM,CAAC,EAAE,CAAC;IACjB;IACF,OAAO,IAAI;EACb;EACA,SAASC,kBAAkBA,CAACC,KAAK,EAAE;IACjC,OAAO,EAAE,GAAGA,KAAK;EACnB;EACA,SAASC,sBAAsBA,CAACD,KAAK,EAAE;IACrC,IAAI;MACFD,kBAAkB,CAACC,KAAK,CAAC;MACzB,IAAIE,wBAAwB,GAAG,CAAC,CAAC;IACnC,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVD,wBAAwB,GAAG,CAAC,CAAC;IAC/B;IACA,IAAIA,wBAAwB,EAAE;MAC5BA,wBAAwB,GAAGjB,OAAO;MAClC,IAAImB,qBAAqB,GAAGF,wBAAwB,CAAChB,KAAK;MAC1D,IAAImB,iCAAiC,GAClC,UAAU,KAAK,OAAOC,MAAM,IAC3BA,MAAM,CAACC,WAAW,IAClBP,KAAK,CAACM,MAAM,CAACC,WAAW,CAAC,IAC3BP,KAAK,CAACQ,WAAW,CAAC/B,IAAI,IACtB,QAAQ;MACV2B,qBAAqB,CAACK,IAAI,CACxBP,wBAAwB,EACxB,0GAA0G,EAC1GG,iCACF,CAAC;MACD,OAAON,kBAAkB,CAACC,KAAK,CAAC;IAClC;EACF;EACA,SAASU,WAAWA,CAACrC,IAAI,EAAE;IACzB,IAAIA,IAAI,KAAKK,mBAAmB,EAAE,OAAO,IAAI;IAC7C,IACE,QAAQ,KAAK,OAAOL,IAAI,IACxB,IAAI,KAAKA,IAAI,IACbA,IAAI,CAACC,QAAQ,KAAKqB,eAAe,EAEjC,OAAO,OAAO;IAChB,IAAI;MACF,IAAIlB,IAAI,GAAGL,wBAAwB,CAACC,IAAI,CAAC;MACzC,OAAOI,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG,GAAG,OAAO;IAC1C,CAAC,CAAC,OAAOqB,CAAC,EAAE;MACV,OAAO,OAAO;IAChB;EACF;EACA,SAASa,QAAQA,CAAA,EAAG;IAClB,IAAIC,UAAU,GAAGC,oBAAoB,CAACC,CAAC;IACvC,OAAO,IAAI,KAAKF,UAAU,GAAG,IAAI,GAAGA,UAAU,CAACD,QAAQ,CAAC,CAAC;EAC3D;EACA,SAASI,YAAYA,CAAA,EAAG;IACtB,OAAOC,KAAK,CAAC,uBAAuB,CAAC;EACvC;EACA,SAASC,WAAWA,CAACC,MAAM,EAAE;IAC3B,IAAIC,cAAc,CAACV,IAAI,CAACS,MAAM,EAAE,KAAK,CAAC,EAAE;MACtC,IAAIE,MAAM,GAAGC,MAAM,CAACC,wBAAwB,CAACJ,MAAM,EAAE,KAAK,CAAC,CAACK,GAAG;MAC/D,IAAIH,MAAM,IAAIA,MAAM,CAACI,cAAc,EAAE,OAAO,CAAC,CAAC;IAChD;IACA,OAAO,KAAK,CAAC,KAAKN,MAAM,CAACO,GAAG;EAC9B;EACA,SAASC,0BAA0BA,CAACC,KAAK,EAAEnD,WAAW,EAAE;IACtD,SAASoD,qBAAqBA,CAAA,EAAG;MAC/BC,0BAA0B,KACtBA,0BAA0B,GAAG,CAAC,CAAC,EACjC5C,OAAO,CAACC,KAAK,CACX,yOAAyO,EACzOV,WACF,CAAC,CAAC;IACN;IACAoD,qBAAqB,CAACJ,cAAc,GAAG,CAAC,CAAC;IACzCH,MAAM,CAACS,cAAc,CAACH,KAAK,EAAE,KAAK,EAAE;MAClCJ,GAAG,EAAEK,qBAAqB;MAC1BG,YAAY,EAAE,CAAC;IACjB,CAAC,CAAC;EACJ;EACA,SAASC,sCAAsCA,CAAA,EAAG;IAChD,IAAIC,aAAa,GAAG7D,wBAAwB,CAAC,IAAI,CAACC,IAAI,CAAC;IACvD6D,sBAAsB,CAACD,aAAa,CAAC,KACjCC,sBAAsB,CAACD,aAAa,CAAC,GAAG,CAAC,CAAC,EAC5ChD,OAAO,CAACC,KAAK,CACX,6IACF,CAAC,CAAC;IACJ+C,aAAa,GAAG,IAAI,CAACN,KAAK,CAACQ,GAAG;IAC9B,OAAO,KAAK,CAAC,KAAKF,aAAa,GAAGA,aAAa,GAAG,IAAI;EACxD;EACA,SAASG,YAAYA,CACnB/D,IAAI,EACJoD,GAAG,EACHY,IAAI,EACJC,MAAM,EACNC,KAAK,EACLZ,KAAK,EACLa,UAAU,EACVC,SAAS,EACT;IACAJ,IAAI,GAAGV,KAAK,CAACQ,GAAG;IAChB9D,IAAI,GAAG;MACLC,QAAQ,EAAEoE,kBAAkB;MAC5BrE,IAAI,EAAEA,IAAI;MACVoD,GAAG,EAAEA,GAAG;MACRE,KAAK,EAAEA,KAAK;MACZgB,MAAM,EAAEJ;IACV,CAAC;IACD,IAAI,MAAM,KAAK,CAAC,KAAKF,IAAI,GAAGA,IAAI,GAAG,IAAI,CAAC,GACpChB,MAAM,CAACS,cAAc,CAACzD,IAAI,EAAE,KAAK,EAAE;MACjCuE,UAAU,EAAE,CAAC,CAAC;MACdrB,GAAG,EAAES;IACP,CAAC,CAAC,GACFX,MAAM,CAACS,cAAc,CAACzD,IAAI,EAAE,KAAK,EAAE;MAAEuE,UAAU,EAAE,CAAC,CAAC;MAAE5C,KAAK,EAAE;IAAK,CAAC,CAAC;IACvE3B,IAAI,CAACwE,MAAM,GAAG,CAAC,CAAC;IAChBxB,MAAM,CAACS,cAAc,CAACzD,IAAI,CAACwE,MAAM,EAAE,WAAW,EAAE;MAC9Cd,YAAY,EAAE,CAAC,CAAC;MAChBa,UAAU,EAAE,CAAC,CAAC;MACdE,QAAQ,EAAE,CAAC,CAAC;MACZ9C,KAAK,EAAE;IACT,CAAC,CAAC;IACFqB,MAAM,CAACS,cAAc,CAACzD,IAAI,EAAE,YAAY,EAAE;MACxC0D,YAAY,EAAE,CAAC,CAAC;MAChBa,UAAU,EAAE,CAAC,CAAC;MACdE,QAAQ,EAAE,CAAC,CAAC;MACZ9C,KAAK,EAAE;IACT,CAAC,CAAC;IACFqB,MAAM,CAACS,cAAc,CAACzD,IAAI,EAAE,aAAa,EAAE;MACzC0D,YAAY,EAAE,CAAC,CAAC;MAChBa,UAAU,EAAE,CAAC,CAAC;MACdE,QAAQ,EAAE,CAAC,CAAC;MACZ9C,KAAK,EAAEwC;IACT,CAAC,CAAC;IACFnB,MAAM,CAACS,cAAc,CAACzD,IAAI,EAAE,YAAY,EAAE;MACxC0D,YAAY,EAAE,CAAC,CAAC;MAChBa,UAAU,EAAE,CAAC,CAAC;MACdE,QAAQ,EAAE,CAAC,CAAC;MACZ9C,KAAK,EAAEyC;IACT,CAAC,CAAC;IACFpB,MAAM,CAAC0B,MAAM,KAAK1B,MAAM,CAAC0B,MAAM,CAAC1E,IAAI,CAACsD,KAAK,CAAC,EAAEN,MAAM,CAAC0B,MAAM,CAAC1E,IAAI,CAAC,CAAC;IACjE,OAAOA,IAAI;EACb;EACA,SAAS2E,UAAUA,CACjB3E,IAAI,EACJ6C,MAAM,EACN+B,QAAQ,EACRC,gBAAgB,EAChBZ,MAAM,EACND,IAAI,EACJG,UAAU,EACVC,SAAS,EACT;IACA,IAAIU,QAAQ,GAAGjC,MAAM,CAACiC,QAAQ;IAC9B,IAAI,KAAK,CAAC,KAAKA,QAAQ,EACrB,IAAID,gBAAgB;MAClB,IAAIE,WAAW,CAACD,QAAQ,CAAC,EAAE;QACzB,KACED,gBAAgB,GAAG,CAAC,EACpBA,gBAAgB,GAAGC,QAAQ,CAACE,MAAM,EAClCH,gBAAgB,EAAE,EAElBI,iBAAiB,CAACH,QAAQ,CAACD,gBAAgB,CAAC,CAAC;QAC/C7B,MAAM,CAAC0B,MAAM,IAAI1B,MAAM,CAAC0B,MAAM,CAACI,QAAQ,CAAC;MAC1C,CAAC,MACClE,OAAO,CAACC,KAAK,CACX,sJACF,CAAC;IAAC,OACDoE,iBAAiB,CAACH,QAAQ,CAAC;IAClC,IAAIhC,cAAc,CAACV,IAAI,CAACS,MAAM,EAAE,KAAK,CAAC,EAAE;MACtCiC,QAAQ,GAAG/E,wBAAwB,CAACC,IAAI,CAAC;MACzC,IAAIkF,IAAI,GAAGlC,MAAM,CAACkC,IAAI,CAACrC,MAAM,CAAC,CAACsC,MAAM,CAAC,UAAUC,CAAC,EAAE;QACjD,OAAO,KAAK,KAAKA,CAAC;MACpB,CAAC,CAAC;MACFP,gBAAgB,GACd,CAAC,GAAGK,IAAI,CAACF,MAAM,GACX,iBAAiB,GAAGE,IAAI,CAACG,IAAI,CAAC,SAAS,CAAC,GAAG,QAAQ,GACnD,gBAAgB;MACtBC,qBAAqB,CAACR,QAAQ,GAAGD,gBAAgB,CAAC,KAC9CK,IAAI,GACJ,CAAC,GAAGA,IAAI,CAACF,MAAM,GAAG,GAAG,GAAGE,IAAI,CAACG,IAAI,CAAC,SAAS,CAAC,GAAG,QAAQ,GAAG,IAAI,EAChEzE,OAAO,CAACC,KAAK,CACX,iOAAiO,EACjOgE,gBAAgB,EAChBC,QAAQ,EACRI,IAAI,EACJJ,QACF,CAAC,EACAQ,qBAAqB,CAACR,QAAQ,GAAGD,gBAAgB,CAAC,GAAG,CAAC,CAAE,CAAC;IAC9D;IACAC,QAAQ,GAAG,IAAI;IACf,KAAK,CAAC,KAAKF,QAAQ,KAChBhD,sBAAsB,CAACgD,QAAQ,CAAC,EAAGE,QAAQ,GAAG,EAAE,GAAGF,QAAS,CAAC;IAChEhC,WAAW,CAACC,MAAM,CAAC,KAChBjB,sBAAsB,CAACiB,MAAM,CAACO,GAAG,CAAC,EAAG0B,QAAQ,GAAG,EAAE,GAAGjC,MAAM,CAACO,GAAI,CAAC;IACpE,IAAI,KAAK,IAAIP,MAAM,EAAE;MACnB+B,QAAQ,GAAG,CAAC,CAAC;MACb,KAAK,IAAIW,QAAQ,IAAI1C,MAAM,EACzB,KAAK,KAAK0C,QAAQ,KAAKX,QAAQ,CAACW,QAAQ,CAAC,GAAG1C,MAAM,CAAC0C,QAAQ,CAAC,CAAC;IACjE,CAAC,MAAMX,QAAQ,GAAG/B,MAAM;IACxBiC,QAAQ,IACNzB,0BAA0B,CACxBuB,QAAQ,EACR,UAAU,KAAK,OAAO5E,IAAI,GACtBA,IAAI,CAACG,WAAW,IAAIH,IAAI,CAACI,IAAI,IAAI,SAAS,GAC1CJ,IACN,CAAC;IACH,OAAO+D,YAAY,CACjB/D,IAAI,EACJ8E,QAAQ,EACRd,IAAI,EACJC,MAAM,EACN3B,QAAQ,CAAC,CAAC,EACVsC,QAAQ,EACRT,UAAU,EACVC,SACF,CAAC;EACH;EACA,SAASa,iBAAiBA,CAACO,IAAI,EAAE;IAC/B,QAAQ,KAAK,OAAOA,IAAI,IACtB,IAAI,KAAKA,IAAI,IACbA,IAAI,CAACvF,QAAQ,KAAKoE,kBAAkB,IACpCmB,IAAI,CAAChB,MAAM,KACVgB,IAAI,CAAChB,MAAM,CAACiB,SAAS,GAAG,CAAC,CAAC;EAC/B;EACA,IAAIC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;IAC1BtB,kBAAkB,GAAGpC,MAAM,CAAC2D,GAAG,CAAC,4BAA4B,CAAC;IAC7D9E,iBAAiB,GAAGmB,MAAM,CAAC2D,GAAG,CAAC,cAAc,CAAC;IAC9CvF,mBAAmB,GAAG4B,MAAM,CAAC2D,GAAG,CAAC,gBAAgB,CAAC;IAClDrF,sBAAsB,GAAG0B,MAAM,CAAC2D,GAAG,CAAC,mBAAmB,CAAC;IACxDtF,mBAAmB,GAAG2B,MAAM,CAAC2D,GAAG,CAAC,gBAAgB,CAAC;EACpD3D,MAAM,CAAC2D,GAAG,CAAC,gBAAgB,CAAC;EAC5B,IAAI5E,mBAAmB,GAAGiB,MAAM,CAAC2D,GAAG,CAAC,gBAAgB,CAAC;IACpD7E,kBAAkB,GAAGkB,MAAM,CAAC2D,GAAG,CAAC,eAAe,CAAC;IAChD1E,sBAAsB,GAAGe,MAAM,CAAC2D,GAAG,CAAC,mBAAmB,CAAC;IACxDpF,mBAAmB,GAAGyB,MAAM,CAAC2D,GAAG,CAAC,gBAAgB,CAAC;IAClDnF,wBAAwB,GAAGwB,MAAM,CAAC2D,GAAG,CAAC,qBAAqB,CAAC;IAC5DvE,eAAe,GAAGY,MAAM,CAAC2D,GAAG,CAAC,YAAY,CAAC;IAC1CtE,eAAe,GAAGW,MAAM,CAAC2D,GAAG,CAAC,YAAY,CAAC;IAC1ClF,mBAAmB,GAAGuB,MAAM,CAAC2D,GAAG,CAAC,gBAAgB,CAAC;IAClD1F,sBAAsB,GAAG+B,MAAM,CAAC2D,GAAG,CAAC,wBAAwB,CAAC;IAC7DpD,oBAAoB,GAClBkD,KAAK,CAACG,+DAA+D;IACvE/C,cAAc,GAAGE,MAAM,CAAC8C,SAAS,CAAChD,cAAc;IAChDiC,WAAW,GAAGgB,KAAK,CAACC,OAAO;IAC3BC,UAAU,GAAGrF,OAAO,CAACqF,UAAU,GAC3BrF,OAAO,CAACqF,UAAU,GAClB,YAAY;MACV,OAAO,IAAI;IACb,CAAC;EACPP,KAAK,GAAG;IACN,0BAA0B,EAAE,SAAAQ,CAAUC,iBAAiB,EAAE;MACvD,OAAOA,iBAAiB,CAAC,CAAC;IAC5B;EACF,CAAC;EACD,IAAI3C,0BAA0B;EAC9B,IAAIK,sBAAsB,GAAG,CAAC,CAAC;EAC/B,IAAIuC,sBAAsB,GAAGV,KAAK,CAAC,0BAA0B,CAAC,CAACW,IAAI,CACjEX,KAAK,EACLhD,YACF,CAAC,CAAC,CAAC;EACH,IAAI4D,qBAAqB,GAAGL,UAAU,CAAC5D,WAAW,CAACK,YAAY,CAAC,CAAC;EACjE,IAAI4C,qBAAqB,GAAG,CAAC,CAAC;EAC9BiB,OAAO,CAACC,QAAQ,GAAGnG,mBAAmB;EACtCkG,OAAO,CAACE,MAAM,GAAG,UACfzG,IAAI,EACJ6C,MAAM,EACN+B,QAAQ,EACRC,gBAAgB,EAChBZ,MAAM,EACND,IAAI,EACJ;IACA,IAAI0C,gBAAgB,GAClB,GAAG,GAAGlE,oBAAoB,CAACmE,0BAA0B,EAAE;IACzD,OAAOhC,UAAU,CACf3E,IAAI,EACJ6C,MAAM,EACN+B,QAAQ,EACRC,gBAAgB,EAChBZ,MAAM,EACND,IAAI,EACJ0C,gBAAgB,GACZ/D,KAAK,CAAC,uBAAuB,CAAC,GAC9ByD,sBAAsB,EAC1BM,gBAAgB,GAAGT,UAAU,CAAC5D,WAAW,CAACrC,IAAI,CAAC,CAAC,GAAGsG,qBACrD,CAAC;EACH,CAAC;AACH,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}