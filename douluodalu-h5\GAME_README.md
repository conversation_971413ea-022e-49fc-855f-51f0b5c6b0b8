# 斗罗大陆 H5 游戏

一款模仿斗罗大陆的网页游戏，包含角色养成、战斗、充值等核心功能。

## 🎮 游戏特色

### 核心功能
- **角色系统**: 魂师角色，包含等级、经验、属性、魂环等
- **战斗系统**: 回合制战斗，挑战各种魂兽
- **充值系统**: 钻石充值，支持多种支付方式模拟
- **抽卡系统**: 召唤不同稀有度的魂师角色
- **商店系统**: 购买装备、消耗品和特殊物品
- **装备系统**: 武器、护甲、饰品强化角色属性

### 游戏内容
- **魂师角色**: 唐三、小舞、宁荣荣、朱竹清等经典角色
- **魂环系统**: 白、黄、紫、黑、红五种魂环
- **战斗挑战**: 森林狼、岩石熊、火焰鸟、冰霜虎等魂兽
- **货币系统**: 钻石（充值货币）和金币（游戏货币）

## 🚀 快速开始

### 启动游戏
```bash
npm start
```

游戏将在 [http://localhost:3000](http://localhost:3000) 启动

## 🎯 游戏玩法

### 1. 登录系统
- 支持用户名密码登录
- 支持游客快速登录

### 2. 角色养成
- 通过战斗获得经验值升级
- 装备武器、护甲提升属性
- 获得魂环增强实力

### 3. 战斗系统
- 选择不同等级的魂兽挑战
- 普通攻击、技能攻击、防御三种操作
- 获胜后获得经验值和金币奖励

### 4. 充值系统
- 多种钻石充值套餐
- 支持微信、支付宝等支付方式（模拟）
- 充值获得钻石用于抽卡和购买特殊物品

### 5. 抽卡召唤
- 普通召唤：使用钻石召唤高品质角色
- 友情召唤：使用金币召唤基础角色
- 四种稀有度：普通、稀有、史诗、传说

### 6. 商店购买
- 装备商店：购买武器、护甲、饰品
- 消耗品：生命药水、魂力药水、经验卷轴
- 特殊物品：魂环等稀有道具

## 🛠️ 技术栈

- **前端**: React 19 + TypeScript
- **样式**: Styled Components
- **状态管理**: React Context + useReducer
- **构建工具**: Create React App

## 📱 游戏界面

### 登录界面
- 精美的斗罗大陆主题设计
- 支持用户名密码和游客登录

### 主界面
- 角色信息展示
- 六大功能模块入口
- 实时显示角色状态

### 战斗界面
- 回合制战斗系统
- 实时战斗日志
- 角色和敌人状态显示

### 充值界面
- 多种充值套餐
- 支付方式选择
- 安全的模拟支付流程

## 🎨 游戏特效

- 精美的渐变背景和动画效果
- 抽卡时的翻牌动画
- 传说角色的发光特效
- 流畅的界面过渡动画

## 📝 开发说明

这是一个演示项目，展示了现代Web游戏开发的技术和设计理念。所有的支付功能都是模拟实现，不涉及真实的金钱交易。

## 🔧 自定义配置

可以通过修改以下文件来自定义游戏内容：
- `src/context/GameContext.tsx` - 游戏状态和数据结构
- `src/components/` - 各个游戏界面组件
- 角色数据、装备数据、敌人数据等都可以轻松扩展

## 📄 许可证

本项目仅供学习和演示使用。
