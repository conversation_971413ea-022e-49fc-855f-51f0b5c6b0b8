import React, { useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { useGame } from '../context/GameContext';

const glow = keyframes`
  0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
  50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }
  100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
`;

const cardFlip = keyframes`
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
`;

const GachaContainer = styled.div`
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
`;

const Title = styled.h2`
  color: #ffd700;
  margin: 0;
`;

const BackButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid #ffd700;
  color: #ffd700;
  padding: 10px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 215, 0, 0.2);
  }
`;

const GachaSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
`;

const GachaBox = styled.div`
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid #ffd700;
  border-radius: 15px;
  padding: 30px;
  text-align: center;
`;

const GachaTitle = styled.h3`
  color: #ffd700;
  margin-bottom: 15px;
  font-size: 1.5rem;
`;

const GachaDescription = styled.p`
  color: #fff;
  margin-bottom: 20px;
  opacity: 0.9;
`;

const ProbabilityList = styled.div`
  margin-bottom: 20px;
  text-align: left;
`;

const ProbabilityItem = styled.div<{ rarity: string }>`
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  color: ${props => {
    switch (props.rarity) {
      case 'legendary': return '#ff6b6b';
      case 'epic': return '#9b59b6';
      case 'rare': return '#3498db';
      default: return '#fff';
    }
  }};
`;

const GachaButton = styled.button<{ variant?: 'single' | 'ten'; canAfford?: boolean }>`
  width: 100%;
  padding: 15px;
  margin-bottom: 10px;
  border: none;
  border-radius: 10px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => {
    if (!props.canAfford) {
      return `
        background: rgba(255, 255, 255, 0.1);
        color: #666;
        cursor: not-allowed;
      `;
    }
    
    switch (props.variant) {
      case 'ten':
        return `
          background: linear-gradient(45deg, #9b59b6, #8e44ad);
          color: white;
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);
          }
        `;
      default:
        return `
          background: linear-gradient(45deg, #3498db, #2980b9);
          color: white;
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
          }
        `;
    }
  }}
`;

const ResultsContainer = styled.div`
  background: rgba(0, 0, 0, 0.8);
  border-radius: 15px;
  padding: 20px;
  border: 2px solid #ffd700;
`;

const ResultsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
`;

const ResultCard = styled.div<{ rarity: string; isNew?: boolean }>`
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid;
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  position: relative;
  animation: ${props => props.isNew ? cardFlip : 'none'} 0.6s ease-in-out;
  
  ${props => {
    switch (props.rarity) {
      case 'legendary':
        return `
          border-color: #ff6b6b;
          background: linear-gradient(135deg, rgba(255, 107, 107, 0.2), rgba(231, 76, 60, 0.1));
          animation: ${props.isNew ? `${cardFlip} 0.6s ease-in-out, ${glow} 2s infinite` : 'none'};
        `;
      case 'epic':
        return `
          border-color: #9b59b6;
          background: linear-gradient(135deg, rgba(155, 89, 182, 0.2), rgba(142, 68, 173, 0.1));
        `;
      case 'rare':
        return `
          border-color: #3498db;
          background: linear-gradient(135deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.1));
        `;
      default:
        return `
          border-color: #95a5a6;
          background: rgba(149, 165, 166, 0.1);
        `;
    }
  }}
`;

const CharacterAvatar = styled.div`
  font-size: 40px;
  margin-bottom: 10px;
`;

const CharacterName = styled.div`
  color: #ffd700;
  font-weight: bold;
  margin-bottom: 5px;
`;

const CharacterRarity = styled.div<{ rarity: string }>`
  font-size: 12px;
  color: ${props => {
    switch (props.rarity) {
      case 'legendary': return '#ff6b6b';
      case 'epic': return '#9b59b6';
      case 'rare': return '#3498db';
      default: return '#95a5a6';
    }
  }};
`;

const NewBadge = styled.div`
  position: absolute;
  top: -5px;
  right: -5px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
`;

// 角色池
const characterPool = [
  // 普通角色 (70%)
  { id: 'char_common_1', name: '村民甲', rarity: 'common', avatar: '👨', stats: { attack: 10, defense: 8 } },
  { id: 'char_common_2', name: '村民乙', rarity: 'common', avatar: '👩', stats: { attack: 12, defense: 6 } },
  { id: 'char_common_3', name: '士兵', rarity: 'common', avatar: '🛡️', stats: { attack: 15, defense: 12 } },
  
  // 稀有角色 (25%)
  { id: 'char_rare_1', name: '小舞', rarity: 'rare', avatar: '🐰', stats: { attack: 25, defense: 18 } },
  { id: 'char_rare_2', name: '宁荣荣', rarity: 'rare', avatar: '👸', stats: { attack: 20, defense: 15 } },
  { id: 'char_rare_3', name: '朱竹清', rarity: 'rare', avatar: '🐱', stats: { attack: 30, defense: 20 } },
  
  // 史诗角色 (4%)
  { id: 'char_epic_1', name: '戴沐白', rarity: 'epic', avatar: '🦁', stats: { attack: 40, defense: 30 } },
  { id: 'char_epic_2', name: '马红俊', rarity: 'epic', avatar: '🔥', stats: { attack: 45, defense: 25 } },
  
  // 传说角色 (1%)
  { id: 'char_legendary_1', name: '唐三', rarity: 'legendary', avatar: '🧙‍♂️', stats: { attack: 60, defense: 40 } },
];

const GachaPanel: React.FC = () => {
  const { state, dispatch } = useGame();
  const [gachaResults, setGachaResults] = useState<any[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);

  const handleBack = () => {
    dispatch({ type: 'SET_SCENE', payload: 'main' });
  };

  const getRandomCharacter = () => {
    const rand = Math.random() * 100;
    
    if (rand < 1) {
      // 1% 传说
      return characterPool.filter(c => c.rarity === 'legendary')[0];
    } else if (rand < 5) {
      // 4% 史诗
      const epics = characterPool.filter(c => c.rarity === 'epic');
      return epics[Math.floor(Math.random() * epics.length)];
    } else if (rand < 30) {
      // 25% 稀有
      const rares = characterPool.filter(c => c.rarity === 'rare');
      return rares[Math.floor(Math.random() * rares.length)];
    } else {
      // 70% 普通
      const commons = characterPool.filter(c => c.rarity === 'common');
      return commons[Math.floor(Math.random() * commons.length)];
    }
  };

  const performGacha = (count: number, cost: { diamonds?: number; coins?: number }) => {
    if (cost.diamonds && state.user.diamonds < cost.diamonds) {
      alert('钻石不足！');
      return;
    }
    if (cost.coins && state.user.coins < cost.coins) {
      alert('金币不足！');
      return;
    }

    setIsAnimating(true);
    
    // 扣除费用
    const currencyUpdate: any = {};
    if (cost.diamonds) currencyUpdate.diamonds = -cost.diamonds;
    if (cost.coins) currencyUpdate.coins = -cost.coins;
    dispatch({ type: 'UPDATE_CURRENCY', payload: currencyUpdate });

    // 生成结果
    const results: any[] = [];
    for (let i = 0; i < count; i++) {
      const character = getRandomCharacter();
      results.push({
        ...character,
        id: `${character.id}_${Date.now()}_${i}`,
        isNew: true,
      });
    }

    // 延迟显示结果以配合动画
    setTimeout(() => {
      setGachaResults(results);
      setIsAnimating(false);
      
      // 添加角色到队伍
      results.forEach(result => {
        const newCharacter = {
          id: result.id,
          name: result.name,
          level: 1,
          exp: 0,
          maxExp: 100,
          hp: 100 + (result.stats.defense * 2),
          maxHp: 100 + (result.stats.defense * 2),
          mp: 50,
          maxMp: 50,
          attack: result.stats.attack,
          defense: result.stats.defense,
          speed: 20,
          soulPower: 1,
          soulRings: [],
          equipment: [],
          avatar: result.avatar,
        };
        
        dispatch({ type: 'ADD_CHARACTER', payload: newCharacter });
      });
      
      // 清除新标记
      setTimeout(() => {
        setGachaResults(prev => prev.map(r => ({ ...r, isNew: false })));
      }, 2000);
      
    }, 1000);
  };

  const canAffordSingle = state.user.diamonds >= 100;
  const canAffordTen = state.user.diamonds >= 900;

  return (
    <GachaContainer>
      <Header>
        <Title>魂师召唤</Title>
        <BackButton onClick={handleBack}>返回</BackButton>
      </Header>

      <GachaSection>
        <GachaBox>
          <GachaTitle>普通召唤</GachaTitle>
          <GachaDescription>
            使用钻石召唤强大的魂师伙伴！
          </GachaDescription>
          
          <ProbabilityList>
            <ProbabilityItem rarity="legendary">传说角色: 1%</ProbabilityItem>
            <ProbabilityItem rarity="epic">史诗角色: 4%</ProbabilityItem>
            <ProbabilityItem rarity="rare">稀有角色: 25%</ProbabilityItem>
            <ProbabilityItem rarity="common">普通角色: 70%</ProbabilityItem>
          </ProbabilityList>
          
          <GachaButton
            variant="single"
            canAfford={canAffordSingle}
            onClick={() => performGacha(1, { diamonds: 100 })}
            disabled={!canAffordSingle || isAnimating}
          >
            单次召唤 (💎100)
          </GachaButton>
          
          <GachaButton
            variant="ten"
            canAfford={canAffordTen}
            onClick={() => performGacha(10, { diamonds: 900 })}
            disabled={!canAffordTen || isAnimating}
          >
            十连召唤 (💎900) 优惠!
          </GachaButton>
        </GachaBox>

        <GachaBox>
          <GachaTitle>友情召唤</GachaTitle>
          <GachaDescription>
            使用金币进行友情召唤，获得基础角色！
          </GachaDescription>
          
          <ProbabilityList>
            <ProbabilityItem rarity="rare">稀有角色: 5%</ProbabilityItem>
            <ProbabilityItem rarity="common">普通角色: 95%</ProbabilityItem>
          </ProbabilityList>
          
          <GachaButton
            variant="single"
            canAfford={state.user.coins >= 1000}
            onClick={() => performGacha(1, { coins: 1000 })}
            disabled={state.user.coins < 1000 || isAnimating}
          >
            友情召唤 (🪙1000)
          </GachaButton>
        </GachaBox>
      </GachaSection>

      {gachaResults.length > 0 && (
        <ResultsContainer>
          <h3 style={{ color: '#ffd700', marginBottom: '20px', textAlign: 'center' }}>
            召唤结果
          </h3>
          <ResultsGrid>
            {gachaResults.map((result, index) => (
              <ResultCard key={index} rarity={result.rarity} isNew={result.isNew}>
                {result.isNew && <NewBadge>!</NewBadge>}
                <CharacterAvatar>{result.avatar}</CharacterAvatar>
                <CharacterName>{result.name}</CharacterName>
                <CharacterRarity rarity={result.rarity}>
                  {result.rarity === 'legendary' ? '传说' :
                   result.rarity === 'epic' ? '史诗' :
                   result.rarity === 'rare' ? '稀有' : '普通'}
                </CharacterRarity>
              </ResultCard>
            ))}
          </ResultsGrid>
        </ResultsContainer>
      )}

      {isAnimating && (
        <div style={{ 
          textAlign: 'center', 
          color: '#ffd700', 
          fontSize: '1.5rem', 
          margin: '50px 0' 
        }}>
          召唤中...✨
        </div>
      )}
    </GachaContainer>
  );
};

export default GachaPanel;
